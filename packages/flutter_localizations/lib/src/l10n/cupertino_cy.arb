{"timerPickerHourLabelTwo": "awr", "datePickerHourSemanticsLabelZero": "$hour o'r gloch", "datePickerHourSemanticsLabelTwo": "$hour o'r gloch", "datePickerHourSemanticsLabelFew": "$hour o'r gloch", "datePickerHourSemanticsLabelMany": "$hour o'r gloch", "timerPickerSecondLabelFew": "eiliad", "timerPickerSecondLabelTwo": "eiliad", "datePickerMinuteSemanticsLabelZero": "$minute munud", "datePickerMinuteSemanticsLabelTwo": "$minute funud", "datePickerMinuteSemanticsLabelFew": "$minute munud", "datePickerMinuteSemanticsLabelMany": "$minute munud", "timerPickerSecondLabelZero": "eiliad", "timerPickerMinuteLabelMany": "munud", "timerPickerMinuteLabelTwo": "funud", "timerPickerMinuteLabelZero": "munud", "timerPickerHourLabelMany": "awr", "timerPickerHourLabelFew": "awr", "timerPickerMinuteLabelFew": "munud", "timerPickerSecondLabelMany": "eiliad", "timerPickerHourLabelZero": "awr", "datePickerHourSemanticsLabelOne": "$hour o'r gloch", "datePickerHourSemanticsLabelOther": "$hour o'r gloch", "datePickerMinuteSemanticsLabelOne": "1 funud", "datePickerMinuteSemanticsLabelOther": "$minute munud", "datePickerDateOrder": "dmy", "datePickerDateTimeOrder": "date_time_dayPeriod", "anteMeridiemAbbreviation": "AM", "postMeridiemAbbreviation": "PM", "todayLabel": "<PERSON><PERSON><PERSON><PERSON>", "alertDialogLabel": "Rhybudd", "tabSemanticsLabel": "Tab $tabIndex o $tabCount", "timerPickerHourLabelOne": "awr", "timerPickerHourLabelOther": "awr", "timerPickerMinuteLabelOne": "funud", "timerPickerMinuteLabelOther": "munud", "timerPickerSecondLabelOne": "eiliad", "timerPickerSecondLabelOther": "eiliad", "cutButtonLabel": "<PERSON><PERSON>", "copyButtonLabel": "<PERSON><PERSON><PERSON><PERSON>", "pasteButtonLabel": "<PERSON><PERSON><PERSON>", "clearButtonLabel": "<PERSON><PERSON><PERSON>", "selectAllButtonLabel": "<PERSON><PERSON><PERSON>", "searchTextFieldPlaceholderLabel": "<PERSON><PERSON><PERSON>", "modalBarrierDismissLabel": "<PERSON><PERSON><PERSON><PERSON>", "noSpellCheckReplacementsLabel": "<PERSON><PERSON>od<PERSON> wed<PERSON>'u Canfod", "menuDismissLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>", "lookUpButtonLabel": "<PERSON><PERSON><PERSON>", "searchWebButtonLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shareButtonLabel": "<PERSON><PERSON><PERSON>...", "cancelButtonLabel": "Canslo", "backButtonLabel": "Nôl"}