{"_comment": ["The builds defined in this file should not contain tests, ", "and the file should not contain builds that are essentially tests. ", "The only builds in this file should be the builds necessary to produce ", "release artifacts. ", "Tests to run on windows hosts should go in one of the other windows_ build ", "definition files."], "builds": [{"archives": [{"base_path": "out/ci/host_debug/zip_archives/", "type": "gcs", "include_paths": ["out/ci/host_debug/zip_archives/windows-x64/artifacts.zip", "out/ci/host_debug/zip_archives/windows-x64/impeller_sdk.zip", "out/ci/host_debug/zip_archives/windows-x64/windows-x64-embedder.zip", "out/ci/host_debug/zip_archives/windows-x64/font-subset.zip", "out/ci/host_debug/zip_archives/dart-sdk-windows-x64.zip", "out/ci/host_debug/zip_archives/windows-x64-debug/windows-x64-flutter.zip", "out/ci/host_debug/zip_archives/windows-x64/flutter-cpp-client-wrapper.zip"], "name": "ci/host_debug", "realm": "production"}], "drone_dimensions": ["device_type=none", "os=Windows-10"], "gclient_variables": {"download_android_deps": false, "download_jdk": false, "use_rbe": true}, "gn": ["--target-dir", "ci/host_debug", "--runtime-mode", "debug", "--no-lto", "--no-goma", "--rbe"], "name": "ci\\host_debug", "description": "Produces debug mode Windows host-side tooling and builds host-side unit tests for Windows.", "ninja": {"config": "ci/host_debug", "targets": ["flutter:unittests", "flutter/build/archives:artifacts", "flutter/build/archives:embedder", "flutter/tools/font_subset", "flutter/build/archives:dart_sdk_archive", "flutter/shell/platform/windows/client_wrapper:client_wrapper_archive", "flutter/build/archives:windows_flutter", "flutter/impeller/toolkit/interop:sdk"]}}, {"archives": [{"base_path": "out/ci/host_profile/zip_archives/", "type": "gcs", "include_paths": ["out/ci/host_profile/zip_archives/windows-x64-profile/windows-x64-flutter.zip"], "name": "ci/host_profile", "realm": "production"}], "drone_dimensions": ["device_type=none", "os=Windows-10"], "gclient_variables": {"download_android_deps": false, "download_jdk": false, "use_rbe": true}, "gn": ["--target-dir", "ci/host_profile", "--runtime-mode", "profile", "--no-lto", "--no-goma", "--rbe"], "name": "ci\\host_profile", "description": "Produces profile mode Windows host-side tooling.", "ninja": {"config": "ci/host_profile", "targets": ["windows", "gen_snapshot", "flutter/build/archives:windows_flutter"]}}, {"archives": [{"base_path": "out/ci/host_release/zip_archives/", "type": "gcs", "include_paths": ["out/ci/host_release/zip_archives/windows-x64-release/windows-x64-flutter.zip"], "name": "ci/host_release", "realm": "production"}], "drone_dimensions": ["device_type=none", "os=Windows-10"], "gclient_variables": {"download_android_deps": false, "download_jdk": false, "use_rbe": true}, "generators": {}, "gn": ["--target-dir", "ci/host_release", "--runtime-mode", "release", "--no-lto", "--no-goma", "--rbe"], "name": "ci\\host_release", "description": "Produces release mode Windows host-side tooling.", "ninja": {"config": "ci/host_release", "targets": ["windows", "gen_snapshot", "flutter/build/archives:windows_flutter"]}}]}