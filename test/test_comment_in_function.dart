import 'package:fluent_lang/core/parser.dart';

void main() {
  print('=== Test Comment in Function ===');
  
  // Test 1: Function without comment
  final code1 = '''
fun size(): num {
    if this.items == null {
        return 0;
    } else {
        return 1;
    }
}
''';
  
  print('--- Test 1: Function without comment ---');
  testCode(code1);
  
  // Test 2: Function with comment at end
  final code2 = '''
fun size(): num {
    if this.items == null {
        return 0;
    } else {
        return 1;
    }
    // comment at end
}
''';
  
  print('\n--- Test 2: Function with comment at end ---');
  testCode(code2);
  
  // Test 3: Function with comment in middle
  final code3 = '''
fun size(): num {
    if this.items == null {
        return 0;
    }
    // comment in middle
    else {
        return 1;
    }
}
''';
  
  print('\n--- Test 3: Function with comment in middle ---');
  testCode(code3);
  
  // Test 4: Function with multiple comments
  final code4 = '''
fun size(): num {
    // start comment
    if this.items == null {
        return 0;
    }
    // middle comment 1
    // middle comment 2
    // middle comment 3
    else {
        return 1;
    }
    // end comment
}
''';
  
  print('\n--- Test 4: Function with multiple comments ---');
  testCode(code4);
}

void testCode(String code) {
  try {
    final parser = buildParser(verbose: false);
    final result = parser.parse(code);
    
    if (result.isSuccess) {
      print('✅ Parse thành công');
    } else {
      print('❌ Parse thất bại: ${result.message}');
      print('📍 Position: ${result.position}');
      if (result.position < code.length) {
        final start = (result.position - 20).clamp(0, code.length);
        final end = (result.position + 20).clamp(0, code.length);
        print('📝 Context: ${code.substring(start, end)}');
      }
    }
  } catch (e) {
    print('❌ Exception: $e');
  }
}
