import 'package:petitparser/petitparser.dart';

void main() {
  print('=== Debug Comment Parser ===');
  
  // Test comment parser directly
  final comment = (string('//') | string('///'))
      .seq(any().starLazy(newline() | endOfInput()).flatten())
      .seq((newline() | endOfInput()))
      .map((v) => 'COMMENT: ${v[1]}');
  
  final testCases = [
    '// simple comment\n',
    '// comment without newline',
    '// comment\nelse',
  ];
  
  for (int i = 0; i < testCases.length; i++) {
    final input = testCases[i];
    print('\nTest ${i + 1}: "${input.replaceAll('\n', '\\n')}"');
    
    final result = comment.parse(input);
    if (result is Success) {
      print('✅ Success: ${result.value}');
      print('Position: ${result.position}');
    } else {
      print('❌ Failed: ${result.message}');
      print('Position: ${result.position}');
    }
  }
  
  // Test comment in context
  print('\n--- Test Comment in Context ---');
  final stmtParser = string('if').trim()
      .seq(string('condition').trim())
      .seq(string('{').trim())
      .seq(string('body').trim())
      .seq(string('}').trim())
      .map((v) => 'IF_STMT');
  
  final commentParser = comment.map((v) => null);
  final stmtOrComment = (commentParser | stmtParser);
  
  final input = '''if condition { body }
// comment here
else''';
  
  print('Input: ${input.replaceAll('\n', '\\n')}');
  
  final result = stmtOrComment.star().parse(input);
  if (result.isSuccess) {
    print('✅ Success: ${result.value}');
    print('Position: ${result.position}');
  } else {
    print('❌ Failed: ${result.message}');
    print('Position: ${result.position}');
  }
}
