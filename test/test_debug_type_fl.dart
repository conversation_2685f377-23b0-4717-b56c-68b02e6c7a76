import 'dart:io';
import 'package:fluent_lang/core/parser.dart';
import 'package:fluent_lang/core/compiler.dart';
import 'package:fluent_lang/core/virtual_machine.dart';

void main() {
  print('=== Debug test_type.fl ===');
  
  // Read the file
  final file = File('example/test_type.fl');
  if (!file.existsSync()) {
    print('❌ File example/test_type.fl không tồn tại');
    return;
  }
  
  final code = file.readAsStringSync();
  print('📄 File content length: ${code.length} characters');
  
  // Check if main() is called
  if (!code.contains('main();')) {
    print('⚠️  File không có lệnh gọi main() ở cuối');
    print('🔧 Thêm main(); vào cuối file...');
    
    final fixedCode = code + '\nmain();';
    testCode(fixedCode, 'Fixed code');
  } else {
    testCode(code, 'Original code');
  }
}

void testCode(String code, String description) {
  print('\n--- Testing: $description ---');
  
  try {
    print('🔍 Parsing...');
    final parser = buildParser(verbose: false);
    final parseResult = parser.parse(code);
    
    if (!parseResult.isSuccess) {
      print('❌ Parse thất bại: ${parseResult.message}');
      print('📍 Position: ${parseResult.position}');
      
      if (parseResult.position < code.length) {
        final start = (parseResult.position - 50).clamp(0, code.length);
        final end = (parseResult.position + 50).clamp(0, code.length);
        print('📝 Context: ${code.substring(start, end)}');
      }
      return;
    }
    
    print('✅ Parse thành công');
    print('📊 AST nodes: ${parseResult.value.toString().length} chars');
    
    print('🔧 Compiling...');
    final compiler = Compiler()..verbose = false;
    compiler.compile(parseResult.value);
    final bytecode = compiler.getBytecode();
    
    print('✅ Compile thành công');
    print('📊 Bytecode instructions: ${bytecode.instructions.length}');
    
    // Print first few instructions for debugging
    print('🔍 First 5 instructions:');
    for (int i = 0; i < 5 && i < bytecode.instructions.length; i++) {
      print('  $i: ${bytecode.instructions[i]}');
    }
    
    print('🚀 Running...');
    final vm = VirtualMachine()..verbose = false;
    vm.load(bytecode);
    
    // Capture output
    final originalPrint = print;
    final outputs = <String>[];
    
    // Override print temporarily (this won't work in Dart, but shows the intent)
    vm.run();
    
    print('✅ Execution hoàn thành');
    
  } catch (e, stackTrace) {
    print('❌ Lỗi: $e');
    print('📍 Stack trace: $stackTrace');
  }
}

void testSimpleVersion() {
  print('\n--- Test Simple Version ---');
  
  final simpleCode = '''
struct Point {
  x: num;
  y: num;
}

fun main(): void {
  let p = Point { x: 10, y: 20 };
  print("Point created");
  print(p.x);
  print(p.y);
}

main();
''';

  testCode(simpleCode, 'Simple struct test');
}

void testGenericVersion() {
  print('\n--- Test Generic Version (Simplified) ---');
  
  final genericCode = '''
struct Box<T> {
  value: T;
}

fun main(): void {
  print("Testing generic box");
  let box = Box { value: 42 };
  print("Box created");
  print(box.value);
}

main();
''';

  testCode(genericCode, 'Simple generic test');
}
