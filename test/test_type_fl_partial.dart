import 'dart:io';
import 'package:fluent_lang/core/parser.dart';

void main() {
  print('=== Test test_type.fl Partial ===');
  
  final file = File('example/test_type.fl');
  final code = file.readAsStringSync();
  
  // Test parsing line by line to find the exact issue
  final lines = code.split('\n');
  
  // Test first 15 lines
  testPartialCode(lines.take(15).join('\n'), 'First 15 lines');
  
  // Test first 20 lines
  testPartialCode(lines.take(20).join('\n'), 'First 20 lines');
  
  // Test first 25 lines
  testPartialCode(lines.take(25).join('\n'), 'First 25 lines');
  
  // Test up to line with error
  testPartialCode(lines.take(12).join('\n'), 'Up to line 12');
  
  // Test just the problematic section
  final problematicSection = '''
struct Array<T> {
    items: T?;
    next: Array<T>?;
}

interface IArray<T> {
    fun push(item: T): void;
    fun pop(): T;
    fun size(): num;
}

Array: IArray<T> {
    static fun new(): Array<T> {
        return Array { items: null, next: null };
    }
}
''';
  
  testPartialCode(problematicSection, 'Problematic section only');
}

void testPartialCode(String code, String description) {
  print('\n--- Test: $description ---');
  print('Code length: ${code.length} characters');
  
  try {
    final parser = buildParser(verbose: false);
    final result = parser.parse(code);
    
    if (result.isSuccess) {
      print('✅ Parse thành công');
    } else {
      print('❌ Parse thất bại: ${result.message}');
      print('📍 Position: ${result.position}');
      
      if (result.position < code.length) {
        final start = (result.position - 30).clamp(0, code.length);
        final end = (result.position + 30).clamp(0, code.length);
        print('📝 Context: ${code.substring(start, end)}');
        
        // Show character at position
        if (result.position < code.length) {
          final char = code[result.position];
          print('📍 Character at position: "$char" (${char.codeUnitAt(0)})');
        }
      }
    }
  } catch (e) {
    print('❌ Exception: $e');
  }
}
