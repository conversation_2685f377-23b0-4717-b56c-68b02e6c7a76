import 'package:fluent_lang/core/parser.dart';
import 'package:fluent_lang/core/compiler.dart';
import 'package:fluent_lang/core/virtual_machine.dart';

void main() {
  print('=== Test Comprehensive Features: Generic, Nullable, Traits ===');
  
  testGenericWithTraitBounds();
  testNullableTypes();
  testComplexGenericScenario();
}

void testGenericWithTraitBounds() {
  print('\n--- Test 1: Generic với Trait Bounds ---');
  
  final code = '''
interface Drawable {
  fun draw(): void;
}

interface Serializable {
  fun serialize(): string;
}

struct Circle {
  radius: num;
}

Circle: Drawable {
  fun draw(): void {
    print("Drawing circle with radius");
    print(this.radius);
  }
}

Circle: Serializable {
  fun serialize(): string {
    return "Circle(radius=" + this.radius + ")";
  }
}

struct Container<T> {
  item: T;
}

Container: Drawable {
  fun draw(): void {
    print("Drawing container");
  }
}

fun main(): void {
  let circle = Circle { radius: 5 };
  circle.draw();
  
  let container = Container<Circle> { item: circle };
  container.draw();
  print(container.item.serialize());
}

main();
''';

  try {
    final parser = buildParser(verbose: false);
    final parseResult = parser.parse(code);
    
    if (!parseResult.isSuccess) {
      print('❌ Parse thất bại: ${parseResult.message}');
      return;
    }
    
    print('✅ Parse thành công');
    
    final compiler = Compiler()..verbose = false;
    compiler.compile(parseResult.value);
    final bytecode = compiler.getBytecode();
    
    print('✅ Compile thành công');
    
    final vm = VirtualMachine()..verbose = false;
    vm.load(bytecode);
    vm.run();
    
    print('✅ Execution thành công');
    
  } catch (e) {
    print('❌ Lỗi: $e');
  }
}

void testNullableTypes() {
  print('\n--- Test 2: Nullable Types ---');
  
  final code = '''
struct User {
  name: string;
  email: string?;
}

fun processUser(user: User?): void {
  if user != null {
    print("User name: " + user.name);
    if user.email != null {
      print("Email: " + user.email);
    } else {
      print("No email provided");
    }
  } else {
    print("User is null");
  }
}

fun main(): void {
  let user1 = User { name: "Alice", email: "<EMAIL>" };
  let user2 = User { name: "Bob", email: null };
  
  processUser(user1);
  processUser(user2);
  processUser(null);
}

main();
''';

  try {
    final parser = buildParser(verbose: false);
    final parseResult = parser.parse(code);
    
    if (!parseResult.isSuccess) {
      print('❌ Parse thất bại: ${parseResult.message}');
      return;
    }
    
    print('✅ Parse thành công');
    
    final compiler = Compiler()..verbose = false;
    compiler.compile(parseResult.value);
    final bytecode = compiler.getBytecode();
    
    print('✅ Compile thành công');
    
    final vm = VirtualMachine()..verbose = false;
    vm.load(bytecode);
    vm.run();
    
    print('✅ Execution thành công');
    
  } catch (e) {
    print('❌ Lỗi: $e');
  }
}

void testComplexGenericScenario() {
  print('\n--- Test 3: Complex Generic Scenario ---');
  
  final code = '''
interface Comparable<T> {
  fun compare(other: T): num;
}

struct Point {
  x: num;
  y: num;
}

Point: Comparable<Point> {
  static fun new(x: num, y: num): Point {
    return Point { x: x, y: y };
  }
  
  fun compare(other: Point): num {
    let dist1 = this.x * this.x + this.y * this.y;
    let dist2 = other.x * other.x + other.y * other.y;
    if dist1 < dist2 {
      return -1;
    } else if dist1 > dist2 {
      return 1;
    } else {
      return 0;
    }
  }
  
  fun toString(): string {
    return "Point(" + this.x + ", " + this.y + ")";
  }
}

struct List<T> {
  items: T?;
  next: List<T>?;
}

List: Comparable<List<T>> {
  static fun new(): List<T> {
    return List { items: null, next: null };
  }
  
  fun add(item: T): void {
    if this.items == null {
      this.items = item;
    } else {
      let newNode = List::new();
      newNode.items = this.items;
      newNode.next = this.next;
      this.items = item;
      this.next = newNode;
    }
  }
  
  fun size(): num {
    if this.items == null {
      return 0;
    } else if this.next == null {
      return 1;
    } else {
      return 1 + this.next.size();
    }
  }
  
  fun compare(other: List<T>): num {
    let size1 = this.size();
    let size2 = other.size();
    if size1 < size2 {
      return -1;
    } else if size1 > size2 {
      return 1;
    } else {
      return 0;
    }
  }
}

fun main(): void {
  let p1 = Point::new(1, 2);
  let p2 = Point::new(3, 4);
  
  print("Comparing points:");
  print(p1.compare(p2));
  
  let list1 = List<Point>::new();
  list1.add(p1);
  list1.add(p2);
  
  let list2 = List<Point>::new();
  list2.add(p1);
  
  print("List sizes:");
  print(list1.size());
  print(list2.size());
  
  print("Comparing lists:");
  print(list1.compare(list2));
}

main();
''';

  try {
    final parser = buildParser(verbose: false);
    final parseResult = parser.parse(code);
    
    if (!parseResult.isSuccess) {
      print('❌ Parse thất bại: ${parseResult.message}');
      print('Position: ${parseResult.position}');
      if (parseResult.position < code.length) {
        final start = (parseResult.position - 30).clamp(0, code.length);
        final end = (parseResult.position + 30).clamp(0, code.length);
        print('Context: ${code.substring(start, end)}');
      }
      return;
    }
    
    print('✅ Parse thành công');
    
    final compiler = Compiler()..verbose = false;
    compiler.compile(parseResult.value);
    final bytecode = compiler.getBytecode();
    
    print('✅ Compile thành công');
    print('Bytecode instructions: ${bytecode.instructions.length}');
    
    final vm = VirtualMachine()..verbose = false;
    vm.load(bytecode);
    vm.run();
    
    print('✅ Execution thành công');
    
  } catch (e) {
    print('❌ Lỗi: $e');
  }
}
