import 'package:fluent_lang/core/parser.dart';

void main() {
  print('=== Test Struct Literal ===');
  
  // Test 1: Simple struct literal
  testSimpleStructLiteral();
  
  // Test 2: Struct literal with multiple fields
  testMultipleFieldsStructLiteral();
  
  // Test 3: Nested struct literals
  testNestedStructLiterals();
  
  // Test 4: The exact problematic line
  testProblematicLine();
}

void testSimpleStructLiteral() {
  print('\n--- Test 1: Simple Struct Literal ---');
  
  final code = '''
fun main(): void {
    let todo = Todo { id: 1 };
}
''';

  try {
    final parser = buildParser(verbose: false);
    final result = parser.parse(code);
    
    if (result.isSuccess) {
      print('✅ Simple struct literal thành công');
    } else {
      print('❌ Simple struct literal thất bại: ${result.message}');
      print('Position: ${result.position}');
    }
  } catch (e) {
    print('❌ Exception: $e');
  }
}

void testMultipleFieldsStructLiteral() {
  print('\n--- Test 2: Multiple Fields Struct Literal ---');
  
  final code = '''
fun main(): void {
    let todo = Todo { id: 1, title: "test", completed: false };
}
''';

  try {
    final parser = buildParser(verbose: false);
    final result = parser.parse(code);
    
    if (result.isSuccess) {
      print('✅ Multiple fields struct literal thành công');
    } else {
      print('❌ Multiple fields struct literal thất bại: ${result.message}');
      print('Position: ${result.position}');
      
      if (result.position < code.length) {
        final start = (result.position - 20).clamp(0, code.length);
        final end = (result.position + 20).clamp(0, code.length);
        print('Context: ${code.substring(start, end)}');
      }
    }
  } catch (e) {
    print('❌ Exception: $e');
  }
}

void testNestedStructLiterals() {
  print('\n--- Test 3: Nested Struct Literals ---');
  
  final code = '''
fun main(): void {
    let todos = Todos { todos: Array::new() };
    todos.todos.push(Todo { id: 1, title: "test", completed: false });
}
''';

  try {
    final parser = buildParser(verbose: false);
    final result = parser.parse(code);
    
    if (result.isSuccess) {
      print('✅ Nested struct literals thành công');
    } else {
      print('❌ Nested struct literals thất bại: ${result.message}');
      print('Position: ${result.position}');
      
      if (result.position < code.length) {
        final start = (result.position - 20).clamp(0, code.length);
        final end = (result.position + 20).clamp(0, code.length);
        print('Context: ${code.substring(start, end)}');
      }
    }
  } catch (e) {
    print('❌ Exception: $e');
  }
}

void testProblematicLine() {
  print('\n--- Test 4: Exact Problematic Line ---');
  
  final code = '''
struct Todo {
    id: num;
    title: string;
    completed: bool;
}

fun main(): void {
    todos.todos.push(Todo { id: 1, title: "Learn Fluent", completed: false });
}
''';

  try {
    final parser = buildParser(verbose: true);
    final result = parser.parse(code);
    
    if (result.isSuccess) {
      print('✅ Problematic line thành công');
    } else {
      print('❌ Problematic line thất bại: ${result.message}');
      print('Position: ${result.position}');
      
      if (result.position < code.length) {
        final start = (result.position - 30).clamp(0, code.length);
        final end = (result.position + 30).clamp(0, code.length);
        print('Context: ${code.substring(start, end)}');
      }
    }
  } catch (e) {
    print('❌ Exception: $e');
  }
}
