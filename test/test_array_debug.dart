import 'package:fluent_lang/core/parser.dart';
import 'package:fluent_lang/core/compiler.dart';
import 'package:fluent_lang/core/virtual_machine.dart';

void main() {
  print('=== Debug Array Generic Issue ===');
  
  // Simplified version of Array without generics first
  final simpleCode = '''
struct Array {
  items: num;
  next: Array?;
}

interface IArray {
  fun push(item: num): void;
  fun pop(): num;
}

Array: IArray {
  static fun new(): Array {
    return Array { items: 0, next: null };
  }
  
  fun push(item: num): void {
    this.items = item;
  }
  
  fun pop(): num {
    return this.items;
  }
}

fun main(): void {
  let arr = Array::new();
  arr.push(42);
  print(arr.pop());
}

main();
''';

  print('--- Test Simple Array (No Generics) ---');
  testCode(simpleCode, 'Simple Array');
  
  // Now test with generics
  final genericCode = '''
struct Array<T> {
  items: T?;
  next: Array<T>?;
}

interface IArray<T> {
  fun push(item: T): void;
  fun pop(): T;
}

Array: IArray<T> {
  static fun new(): Array<T> {
    return Array { items: null, next: null };
  }
  
  fun push(item: T): void {
    this.items = item;
  }
  
  fun pop(): T {
    return this.items;
  }
}

fun main(): void {
  let arr = Array::new();
  arr.push(42);
  print(arr.pop());
}

main();
''';

  print('\n--- Test Generic Array ---');
  testCode(genericCode, 'Generic Array');
}

void testCode(String code, String description) {
  print('\n🔍 Testing: $description');
  
  try {
    final parser = buildParser(verbose: false);
    final parseResult = parser.parse(code);
    
    if (!parseResult.isSuccess) {
      print('❌ Parse thất bại: ${parseResult.message}');
      return;
    }
    
    print('✅ Parse thành công');
    
    final compiler = Compiler()..verbose = false;
    compiler.compile(parseResult.value);
    final bytecode = compiler.getBytecode();
    
    print('✅ Compile thành công');
    
    final vm = VirtualMachine()..verbose = false;
    vm.load(bytecode);
    
    // Debug static implementations
    print('📊 Static implementations:');
    for (final entry in vm.staticTraitImpls.entries) {
      print('  ${entry.key}: ${entry.value.keys}');
    }
    
    vm.run();
    
    print('✅ Execution thành công');
    
  } catch (e) {
    print('❌ Lỗi: $e');
  }
}
