import 'package:test/test.dart';

import 'package:fluent_lang/core/parser.dart';

void main() {
  test('Parse full struct + interface + impl', () {
    final code = '''
struct S {
  a: num;
  b: string;
}

interface T {
  fun do_sth(): void;
}

S: T {
 fun new(): S {
    return S { a: 1, b: "hello" };
  }

  fun do_sth(): void {
    print("do something");
  }
}
''';

    final parser = buildParser(verbose: true);
    final result = parser.parse(code);

    if (!result.isSuccess) {
      print("❌ Failed at ${result.position}: ${result.message}");
      print(code.substring(result.position - 10, result.position + 20)); // xem trước-sau
      print("Remaining input: ${code.substring(result.position)}");
    }

    expect(result.isSuccess, true);
  });

}