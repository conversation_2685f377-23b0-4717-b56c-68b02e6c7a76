import 'package:fluent_lang/core/parser.dart';

void main() {
  print('=== Test Comment và Else If ===');
  
  testComment();
  testElseIf();
  testCommentInCode();
}

void testComment() {
  print('\n--- Test Comment Parsing ---');
  
  final testCases = [
    '// simple comment\n',
    '// comment without newline',
    '/// doc comment\n',
    '''
// line 1
// line 2
// line 3
''',
    '''
fun test(): void {
  // comment inside function
  print("hello");
}
''',
  ];
  
  for (int i = 0; i < testCases.length; i++) {
    final code = testCases[i];
    print('\nTest case ${i + 1}: ${code.replaceAll('\n', '\\n')}');
    
    try {
      final parser = buildParser(verbose: true);
      final result = parser.parse(code);
      
      if (result.isSuccess) {
        print('✅ Parse thành công: ${result.value}');
      } else {
        print('❌ Parse thất bại: ${result.message}');
        print('📍 Position: ${result.position}');
      }
    } catch (e) {
      print('❌ Exception: $e');
    }
  }
}

void testElseIf() {
  print('\n--- Test Else If Support ---');
  
  final testCases = [
    '''
fun test(): void {
  if x == 1 {
    print("one");
  } else if x == 2 {
    print("two");
  } else {
    print("other");
  }
}
''',
    '''
fun test(): void {
  if x == 1 {
    print("one");
  } else {
    if x == 2 {
      print("two");
    } else {
      print("other");
    }
  }
}
''',
  ];
  
  for (int i = 0; i < testCases.length; i++) {
    final code = testCases[i];
    print('\nTest case ${i + 1}: else if vs nested if');
    
    try {
      final parser = buildParser(verbose: false);
      final result = parser.parse(code);
      
      if (result.isSuccess) {
        print('✅ Parse thành công');
      } else {
        print('❌ Parse thất bại: ${result.message}');
        print('📍 Position: ${result.position}');
        if (result.position < code.length) {
          final start = (result.position - 20).clamp(0, code.length);
          final end = (result.position + 20).clamp(0, code.length);
          print('📝 Context: ${code.substring(start, end)}');
        }
      }
    } catch (e) {
      print('❌ Exception: $e');
    }
  }
}

void testCommentInCode() {
  print('\n--- Test Comment trong Code ---');
  
  final code = '''
fun size(): num {
    if this.items == null {
        return 0;
    }
    // else if this.next == null {
    //    return 1;
    // }
     else {
        return 1 + this.next.size();
    }
}
''';
  
  print('Testing code with commented else if...');
  
  try {
    final parser = buildParser(verbose: true);
    final result = parser.parse(code);
    
    if (result.isSuccess) {
      print('✅ Parse thành công');
      print('AST: ${result.value}');
    } else {
      print('❌ Parse thất bại: ${result.message}');
      print('📍 Position: ${result.position}');
      if (result.position < code.length) {
        final start = (result.position - 30).clamp(0, code.length);
        final end = (result.position + 30).clamp(0, code.length);
        print('📝 Context: ${code.substring(start, end)}');
      }
    }
  } catch (e) {
    print('❌ Exception: $e');
  }
}
