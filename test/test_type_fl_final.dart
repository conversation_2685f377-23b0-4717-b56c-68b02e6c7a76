import 'dart:io';
import 'package:fluent_lang/core/parser.dart';
import 'package:fluent_lang/core/compiler.dart';
import 'package:fluent_lang/core/virtual_machine.dart';

void main() {
  print('=== Final Test: test_type.fl ===');
  
  final file = File('example/test_type.fl');
  if (!file.existsSync()) {
    print('❌ File example/test_type.fl không tồn tại');
    return;
  }
  
  final code = file.readAsStringSync();
  print('📄 File loaded: ${code.length} characters');
  
  try {
    print('🔍 Parsing...');
    final parser = buildParser(verbose: false);
    final parseResult = parser.parse(code);
    
    if (!parseResult.isSuccess) {
      print('❌ Parse thất bại: ${parseResult.message}');
      print('📍 Position: ${parseResult.position}');
      
      if (parseResult.position < code.length) {
        final start = (parseResult.position - 50).clamp(0, code.length);
        final end = (parseResult.position + 50).clamp(0, code.length);
        print('📝 Context: ${code.substring(start, end)}');
        
        // Show lines around error
        final lines = code.split('\n');
        int lineNum = 1;
        int charCount = 0;
        for (final line in lines) {
          if (charCount <= parseResult.position && parseResult.position <= charCount + line.length) {
            print('📍 Error at line $lineNum: "$line"');
            final col = parseResult.position - charCount;
            print('📍 Column: $col');
            break;
          }
          charCount += line.length + 1; // +1 for newline
          lineNum++;
        }
      }
      return;
    }
    
    print('✅ Parse thành công');
    
    print('🔧 Compiling...');
    final compiler = Compiler()..verbose = false;
    compiler.compile(parseResult.value);
    final bytecode = compiler.getBytecode();
    
    print('✅ Compile thành công');
    print('📊 Bytecode instructions: ${bytecode.instructions.length}');
    
    print('🚀 Running...');
    final vm = VirtualMachine()..verbose = false;
    vm.load(bytecode);
    vm.run();
    
    print('✅ Execution hoàn thành');
    
  } catch (e, stackTrace) {
    print('❌ Lỗi: $e');
    print('📍 Stack trace: $stackTrace');
  }
}
