import 'package:fluent_lang/core/parser.dart';

void main() {
  print('=== Test Generic Trait Implementation ===');
  
  // Test simple trait implementation first
  testSimpleTraitImpl();
  
  // Test generic trait implementation
  testGenericTraitImpl();
  
  // Test the exact line from test_type.fl
  testExactLine();
}

void testSimpleTraitImpl() {
  print('\n--- Test 1: Simple Trait Implementation ---');
  
  final code = '''
Array: IArray {
  static fun new(): Array {
    return Array { items: null, next: null };
  }
}
''';

  try {
    final parser = buildParser(verbose: true);
    final result = parser.parse(code);
    
    if (result.isSuccess) {
      print('✅ Simple trait impl thành công');
    } else {
      print('❌ Simple trait impl thất bại: ${result.message}');
      print('Position: ${result.position}');
    }
  } catch (e) {
    print('❌ Exception: $e');
  }
}

void testGenericTraitImpl() {
  print('\n--- Test 2: Generic Trait Implementation ---');
  
  final code = '''
Array: IArray<T> {
  static fun new(): Array<T> {
    return Array { items: null, next: null };
  }
}
''';

  try {
    final parser = buildParser(verbose: true);
    final result = parser.parse(code);
    
    if (result.isSuccess) {
      print('✅ Generic trait impl thành công');
    } else {
      print('❌ Generic trait impl thất bại: ${result.message}');
      print('Position: ${result.position}');
      if (result.position < code.length) {
        final start = (result.position - 20).clamp(0, code.length);
        final end = (result.position + 20).clamp(0, code.length);
        print('Context: ${code.substring(start, end)}');
      }
    }
  } catch (e) {
    print('❌ Exception: $e');
  }
}

void testExactLine() {
  print('\n--- Test 3: Exact Line from test_type.fl ---');
  
  final code = '''
struct Array<T> {
    items: T?;
    next: Array<T>?;
}

interface IArray<T> {
    fun push(item: T): void;
    fun pop(): T;
    fun size(): num;
}

Array: IArray<T> {
    static fun new(): Array<T> {
        return Array { items: null, next: null };
    }
}
''';

  try {
    final parser = buildParser(verbose: false);
    final result = parser.parse(code);
    
    if (result.isSuccess) {
      print('✅ Exact line test thành công');
    } else {
      print('❌ Exact line test thất bại: ${result.message}');
      print('Position: ${result.position}');
      
      // Show line and column
      final lines = code.split('\n');
      int lineNum = 1;
      int charCount = 0;
      for (final line in lines) {
        if (charCount <= result.position && result.position <= charCount + line.length) {
          print('📍 Error at line $lineNum: "$line"');
          final col = result.position - charCount;
          print('📍 Column: $col');
          break;
        }
        charCount += line.length + 1; // +1 for newline
        lineNum++;
      }
    }
  } catch (e) {
    print('❌ Exception: $e');
  }
}
