import 'package:fluent_lang/core/parser.dart';
import 'package:fluent_lang/core/compiler.dart';
import 'package:fluent_lang/core/virtual_machine.dart';

void main() {
  print('=== Test Generic Types và Trait Bounds ===');
  
  // Test 1: Parse generic struct
  testParseGenericStruct();
  
  // Test 2: Parse generic interface
  testParseGenericInterface();
  
  // Test 3: Parse trait implementation with generic
  testParseTraitImplementation();
  
  // Test 4: Test full compilation
  testFullCompilation();
}

void testParseGenericStruct() {
  print('\n--- Test 1: Parse Generic Struct ---');
  
  final code = '''
struct Box<T> {
  value: T;
}
''';

  final parser = buildParser(verbose: true);
  final result = parser.parse(code);
  
  if (result.isSuccess) {
    print('✅ Parse generic struct thành công');
    print('Result: ${result.value}');
  } else {
    print('❌ Parse generic struct thất bại: ${result.message}');
  }
}

void testParseGenericInterface() {
  print('\n--- Test 2: Parse Generic Interface ---');
  
  final code = '''
interface IContainer<T> {
  fun get(): T;
  fun set(value: T): void;
}
''';

  final parser = buildParser(verbose: true);
  final result = parser.parse(code);
  
  if (result.isSuccess) {
    print('✅ Parse generic interface thành công');
    print('Result: ${result.value}');
  } else {
    print('❌ Parse generic interface thất bại: ${result.message}');
  }
}

void testParseTraitImplementation() {
  print('\n--- Test 3: Parse Trait Implementation with Generic ---');
  
  final code = '''
struct Box<T> {
  value: T;
}

interface IContainer<T> {
  fun get(): T;
  fun set(value: T): void;
}

Box: IContainer<T> {
  static fun new(value: T): Box<T> {
    return Box { value: value };
  }
  
  fun get(): T {
    return this.value;
  }
  
  fun set(value: T): void {
    this.value = value;
  }
}
''';

  final parser = buildParser(verbose: true);
  final result = parser.parse(code);
  
  if (result.isSuccess) {
    print('✅ Parse trait implementation với generic thành công');
    print('Result: ${result.value}');
  } else {
    print('❌ Parse trait implementation với generic thất bại: ${result.message}');
    print('Position: ${result.position}');
    if (result.position < code.length) {
      final start = (result.position - 20).clamp(0, code.length);
      final end = (result.position + 20).clamp(0, code.length);
      print('Context: ${code.substring(start, end)}');
    }
  }
}

void testFullCompilation() {
  print('\n--- Test 4: Full Compilation ---');
  
  final code = '''
struct Box<T> {
  value: T;
}

interface IContainer<T> {
  fun get(): T;
  fun set(value: T): void;
}

Box: IContainer<T> {
  static fun new(value: T): Box<T> {
    return Box { value: value };
  }
  
  fun get(): T {
    return this.value;
  }
  
  fun set(value: T): void {
    this.value = value;
  }
}

fun main(): void {
  let box = Box::new(42);
  print(box.get());
  box.set(100);
  print(box.get());
}

main();
''';

  try {
    print('Parsing...');
    final parser = buildParser(verbose: false);
    final parseResult = parser.parse(code);
    
    if (!parseResult.isSuccess) {
      print('❌ Parse thất bại: ${parseResult.message}');
      return;
    }
    
    print('✅ Parse thành công');
    
    print('Compiling...');
    final compiler = Compiler()..verbose = false;
    compiler.compile(parseResult.value);
    final bytecode = compiler.getBytecode();
    
    print('✅ Compile thành công');
    print('Bytecode instructions: ${bytecode.instructions.length}');
    
    print('Running...');
    final vm = VirtualMachine()..verbose = false;
    vm.load(bytecode);
    vm.run();
    
    print('✅ Execution thành công');
    
  } catch (e) {
    print('❌ Lỗi trong quá trình compilation/execution: $e');
  }
}
