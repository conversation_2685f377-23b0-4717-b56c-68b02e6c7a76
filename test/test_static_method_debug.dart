import 'package:fluent_lang/core/parser.dart';
import 'package:fluent_lang/core/compiler.dart';
import 'package:fluent_lang/core/virtual_machine.dart';

void main() {
  print('=== Debug Static Method Call ===');
  
  final code = '''
struct Point {
  x: num;
  y: num;
}

interface IPoint {
  fun getX(): num;
}

Point: IPoint {
  static fun new(x: num, y: num): Point {
    return Point { x: x, y: y };
  }
  
  fun getX(): num {
    return this.x;
  }
}

fun main(): void {
  let p = Point::new(10, 20);
  print(p.getX());
}

main();
''';

  try {
    print('🔍 Parsing...');
    final parser = buildParser(verbose: false);
    final parseResult = parser.parse(code);
    
    if (!parseResult.isSuccess) {
      print('❌ Parse thất bại: ${parseResult.message}');
      return;
    }
    
    print('✅ Parse thành công');
    
    print('🔧 Compiling...');
    final compiler = Compiler()..verbose = true;
    compiler.compile(parseResult.value);
    final bytecode = compiler.getBytecode();
    
    print('✅ Compile thành công');
    
    print('🚀 Running...');
    final vm = VirtualMachine()..verbose = true;
    vm.load(bytecode);
    
    // Debug: Print static trait implementations
    print('📊 Static trait implementations:');
    for (final entry in vm.staticTraitImpls.entries) {
      print('  ${entry.key}: ${entry.value.keys}');
    }
    
    vm.run();
    
    print('✅ Execution hoàn thành');
    
  } catch (e, stackTrace) {
    print('❌ Lỗi: $e');
    print('📍 Stack trace: $stackTrace');
  }
}
