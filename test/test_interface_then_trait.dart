import 'package:fluent_lang/core/parser.dart';

void main() {
  print('=== Test Interface Then Trait Implementation ===');
  
  // Test 1: Just interface
  testJustInterface();
  
  // Test 2: Interface + trait implementation
  testInterfaceThenTrait();
  
  // Test 3: Multiple declarations
  testMultipleDeclarations();
}

void testJustInterface() {
  print('\n--- Test 1: Just Interface ---');
  
  final code = '''
interface IArray<T> {
    fun push(item: T): void;
    fun pop(): T;
    fun size(): num;
}
''';

  try {
    final parser = buildParser(verbose: false);
    final result = parser.parse(code);
    
    if (result.isSuccess) {
      print('✅ Just interface thành công');
    } else {
      print('❌ Just interface thất bại: ${result.message}');
      print('Position: ${result.position}');
    }
  } catch (e) {
    print('❌ Exception: $e');
  }
}

void testInterfaceThenTrait() {
  print('\n--- Test 2: Interface + Trait Implementation ---');
  
  final code = '''
interface IArray<T> {
    fun push(item: T): void;
    fun pop(): T;
    fun size(): num;
}

Array: IArray<T> {
    static fun new(): Array<T> {
        return Array { items: null, next: null };
    }
}
''';

  try {
    final parser = buildParser(verbose: false);
    final result = parser.parse(code);
    
    if (result.isSuccess) {
      print('✅ Interface + trait thành công');
    } else {
      print('❌ Interface + trait thất bại: ${result.message}');
      print('Position: ${result.position}');
      
      if (result.position < code.length) {
        final start = (result.position - 20).clamp(0, code.length);
        final end = (result.position + 20).clamp(0, code.length);
        print('Context: ${code.substring(start, end)}');
      }
    }
  } catch (e) {
    print('❌ Exception: $e');
  }
}

void testMultipleDeclarations() {
  print('\n--- Test 3: Multiple Declarations ---');
  
  final code = '''
struct Array<T> {
    items: T?;
    next: Array<T>?;
}

interface IArray<T> {
    fun push(item: T): void;
    fun pop(): T;
    fun size(): num;
}

Array: IArray<T> {
    static fun new(): Array<T> {
        return Array { items: null, next: null };
    }
}

fun main(): void {
    print("hello");
}
''';

  try {
    final parser = buildParser(verbose: false);
    final result = parser.parse(code);
    
    if (result.isSuccess) {
      print('✅ Multiple declarations thành công');
    } else {
      print('❌ Multiple declarations thất bại: ${result.message}');
      print('Position: ${result.position}');
      
      if (result.position < code.length) {
        final start = (result.position - 20).clamp(0, code.length);
        final end = (result.position + 20).clamp(0, code.length);
        print('Context: ${code.substring(start, end)}');
      }
    }
  } catch (e) {
    print('❌ Exception: $e');
  }
}
