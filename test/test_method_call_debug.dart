import 'package:fluent_lang/core/parser.dart';
import 'package:fluent_lang/core/compiler.dart';
import 'package:fluent_lang/core/virtual_machine.dart';

void main() {
  print('=== Debug Method Call Issue ===');
  
  testSimpleMethodCall();
  testNullLiteral();
}

void testSimpleMethodCall() {
  print('\n--- Test Simple Method Call ---');
  
  final code = '''
struct Circle {
  radius: num;
}

interface Drawable {
  fun draw(): void;
}

Circle: Drawable {
  fun draw(): void {
    print("Drawing circle");
  }
}

fun main(): void {
  let circle = Circle { radius: 5 };
  circle.draw();
}

main();
''';

  try {
    final parser = buildParser(verbose: true);
    final parseResult = parser.parse(code);
    
    if (!parseResult.isSuccess) {
      print('❌ Parse thất bại: ${parseResult.message}');
      return;
    }
    
    print('✅ Parse thành công');
    
    final compiler = Compiler()..verbose = true;
    compiler.compile(parseResult.value);
    final bytecode = compiler.getBytecode();
    
    print('✅ Compile thành công');
    
    final vm = VirtualMachine()..verbose = true;
    vm.load(bytecode);
    vm.run();
    
    print('✅ Execution thành công');
    
  } catch (e) {
    print('❌ Lỗi: $e');
  }
}

void testNullLiteral() {
  print('\n--- Test Null Literal ---');
  
  final code = '''
fun main(): void {
  let x = null;
  print("x is null");
  if x == null {
    print("x is indeed null");
  }
}

main();
''';

  try {
    final parser = buildParser(verbose: false);
    final parseResult = parser.parse(code);
    
    if (!parseResult.isSuccess) {
      print('❌ Parse thất bại: ${parseResult.message}');
      return;
    }
    
    print('✅ Parse thành công');
    
    final compiler = Compiler()..verbose = false;
    compiler.compile(parseResult.value);
    final bytecode = compiler.getBytecode();
    
    print('✅ Compile thành công');
    
    final vm = VirtualMachine()..verbose = false;
    vm.load(bytecode);
    vm.run();
    
    print('✅ Execution thành công');
    
  } catch (e) {
    print('❌ Lỗi: $e');
  }
}

void testArrayPush() {
  print('\n--- Test Array Push (Simplified) ---');
  
  final code = '''
struct Array {
  value: num;
}

Array: {
  fun push(item: num): void {
    this.value = item;
  }
}

fun main(): void {
  let arr = Array { value: 0 };
  arr.push(42);
  print(arr.value);
}

main();
''';

  try {
    final parser = buildParser(verbose: true);
    final parseResult = parser.parse(code);
    
    if (!parseResult.isSuccess) {
      print('❌ Parse thất bại: ${parseResult.message}');
      return;
    }
    
    print('✅ Parse thành công');
    
    final compiler = Compiler()..verbose = true;
    compiler.compile(parseResult.value);
    final bytecode = compiler.getBytecode();
    
    print('✅ Compile thành công');
    
    final vm = VirtualMachine()..verbose = true;
    vm.load(bytecode);
    vm.run();
    
    print('✅ Execution thành công');
    
  } catch (e) {
    print('❌ Lỗi: $e');
  }
}
