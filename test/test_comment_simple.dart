import 'package:fluent_lang/core/parser.dart';

void main() {
  print('=== Test Comment Simple ===');
  
  final code = '''
fun size(): num {
    if this.items == null {
        return 0;
    }
    // else if this.next == null {
    //    return 1;
    // }
    else {
        return 1 + this.next.size();
    }
}
''';
  
  print('Testing code with commented else if...');
  
  try {
    final parser = buildParser(verbose: false);
    final result = parser.parse(code);
    
    if (result.isSuccess) {
      print('✅ Parse thành công');
      print('AST: ${result.value}');
    } else {
      print('❌ Parse thất bại: ${result.message}');
      print('📍 Position: ${result.position}');
      if (result.position < code.length) {
        final start = (result.position - 30).clamp(0, code.length);
        final end = (result.position + 30).clamp(0, code.length);
        print('📝 Context: ${code.substring(start, end)}');
        
        // Show character at position
        if (result.position < code.length) {
          print('📍 Character at position: "${code[result.position]}" (${code.codeUnitAt(result.position)})');
        }
      }
    }
  } catch (e) {
    print('❌ Exception: $e');
  }
  
  // Test simple comment
  print('\n--- Test Simple Comment ---');
  final simpleComment = '// this is a comment\n';
  
  try {
    final parser = buildParser(verbose: true);
    final result = parser.parse(simpleComment);
    
    if (result.isSuccess) {
      print('✅ Simple comment parse thành công');
    } else {
      print('❌ Simple comment parse thất bại: ${result.message}');
    }
  } catch (e) {
    print('❌ Exception: $e');
  }
}
