import 'package:fluent_lang/core/parser.dart';
import 'package:fluent_lang/core/compiler.dart';
import 'package:fluent_lang/core/virtual_machine.dart';
import 'package:fluent_lang/core/runtime.dart';

void main() {
  print('=== Test Basic Features: Generic, Nullable, Traits ===');

  testBasicGeneric();
  testBasicTrait();
  testBasicStruct();
  testGenericInstantiation();
}

void testBasicGeneric() {
  print('\n--- Test 1: Basic Generic Parsing ---');

  final code = '''
struct Box<T> {
  value: T;
}
''';

  try {
    final parser = buildParser(verbose: true);
    final parseResult = parser.parse(code);

    if (!parseResult.isSuccess) {
      print('❌ Parse thất bại: ${parseResult.message}');
      print('Position: ${parseResult.position}');
      return;
    }

    print('✅ Parse generic struct thành công');
    print('AST: ${parseResult.value}');

    final compiler = Compiler()..verbose = true;
    compiler.compile(parseResult.value);
    final bytecode = compiler.getBytecode();

    print('✅ Compile thành công');
    print('Instructions: ${bytecode.instructions.length}');

  } catch (e) {
    print('❌ Lỗi: $e');
  }
}

void testBasicTrait() {
  print('\n--- Test 2: Basic Trait ---');

  final code = '''
struct Circle {
  radius: num;
}

interface Drawable {
  fun draw(): void;
}

Circle: Drawable {
  fun draw(): void {
    print("Drawing circle");
  }
}

fun main(): void {
  let circle = Circle { radius: 5 };
  circle.draw();
}

main();
''';

  try {
    final parser = buildParser(verbose: false);
    final parseResult = parser.parse(code);

    if (!parseResult.isSuccess) {
      print('❌ Parse thất bại: ${parseResult.message}');
      print('Position: ${parseResult.position}');
      return;
    }

    print('✅ Parse thành công');

    final compiler = Compiler()..verbose = false;
    compiler.compile(parseResult.value);
    final bytecode = compiler.getBytecode();

    print('✅ Compile thành công');

    final vm = VirtualMachine()..verbose = false;
    vm.load(bytecode);
    vm.run();

    print('✅ Execution thành công');

  } catch (e) {
    print('❌ Lỗi: $e');
  }
}

void testBasicStruct() {
  print('\n--- Test 3: Basic Struct Operations ---');

  final code = '''
struct Point {
  x: num;
  y: num;
}

fun main(): void {
  let p = Point { x: 10, y: 20 };
  print(p.x);
  print(p.y);
  p.x = 30;
  print(p.x);
}

main();
''';

  try {
    final parser = buildParser(verbose: false);
    final parseResult = parser.parse(code);

    if (!parseResult.isSuccess) {
      print('❌ Parse thất bại: ${parseResult.message}');
      return;
    }

    print('✅ Parse thành công');

    final compiler = Compiler()..verbose = false;
    compiler.compile(parseResult.value);
    final bytecode = compiler.getBytecode();

    print('✅ Compile thành công');

    final vm = VirtualMachine()..verbose = false;
    vm.load(bytecode);
    vm.run();

    print('✅ Execution thành công');

  } catch (e) {
    print('❌ Lỗi: $e');
  }
}

void testGenericInstantiation() {
  print('\n--- Test 4: Generic Instantiation (Manual) ---');

  // Test manual generic instantiation
  try {
    final vm = VirtualMachine();

    // Manually register a generic type
    final definition = GenericTypeDefinition(
      'Box',
      ['T'],
      {'value': 'T'},
    );

    vm.typeRegistry.registerGenericType(definition);

    // Test instantiation
    print('Type parameters: ${definition.typeParameters}');
    print('Provided type args: ${{'T': 'int'}.keys}');

    final instance = definition.instantiate(
      {'T': 'int'},
      {'value': 42},
    );

    print('✅ Generic instantiation thành công');
    print('Instance: $instance');
    print('Concrete type: ${instance.getConcreteTypeName()}');

  } catch (e) {
    print('❌ Lỗi: $e');
  }
}
