# VM Processing: Generic Types, Nullable Types, và Traits

## Tổng quan

Tài liệu này mô tả chi tiết cách Virtual Machine (VM) của Fluent Lang xử lý Generic Types, Nullable Types, và Traits từ parsing đến runtime execution.

## 1. Generic Types Processing

### 1.1 Parsing Phase
```fluent
struct Box<T> {
    value: T;
}
```

**Parser Output:**
- AST Node: `StructDeclaration` với generic parameters
- Type Parameters: `['T']`
- Fields: `{value: GenericType(T)}`

### 1.2 Compilation Phase

**OpCodes được sinh ra:**
```
DEFINE_GENERIC_STRUCT {
    name: "Box",
    typeParameters: ["T"],
    fields: {"value": "T"},
    parameterBounds: null
}
```

**Compiler Steps:**
1. Detect generic syntax `<T>` trong struct name
2. Extract type parameters từ `<...>`
3. Analyze fields để tìm generic types
4. Generate `DEFINE_GENERIC_STRUCT` thay vì `DEFINE_STRUCT`

### 1.3 Runtime Phase

**VM Processing:**
1. **Type Registry**: Lưu trữ `GenericTypeDefinition`
2. **Instantiation**: Khi gặp `Box<int>`, tạo concrete instance
3. **Type Checking**: Validate type arguments match parameters

**Data Structures:**
```dart
class GenericTypeDefinition {
    String name;                    // "Box"
    List<String> typeParameters;   // ["T"]
    Map<String, String> fields;    // {"value": "T"}
    Map<String, List<String>>? parameterBounds; // {"T": ["Clone", "Eq"]}
}
```

### 1.4 Generic Instantiation

**Code Example:**
```fluent
let box = Box<int> { value: 42 };
```

**VM Steps:**
1. Parse type arguments: `{T: int}`
2. Validate type arguments match parameters
3. Check trait bounds (nếu có)
4. Create `StructInstance` với `typeArguments`
5. Store concrete type mapping

**OpCode Flow:**
```
LOAD_CONST 42
INSTANTIATE_GENERIC {
    baseName: "Box",
    typeArguments: {"T": "int"},
    fieldNames: ["value"]
}
```

## 2. Nullable Types Processing

### 2.1 Syntax Support
```fluent
struct User {
    name: string;
    email: string?;  // Nullable
}
```

### 2.2 Compilation Phase

**Type Annotations:**
- `OptionalType(base: NamedType("string"))`
- Compiler generates null-safe operations

### 2.3 Runtime Operations

**New OpCodes:**
- `NULL_CHECK`: Throw exception if value is null
- `SAFE_GET_FIELD`: Return null if object is null
- `SAFE_METHOD_CALL`: Skip call if receiver is null

**Example Processing:**
```fluent
user?.email?.length()
```

**Generated OpCodes:**
```
LOAD user
SAFE_GET_FIELD "email"
SAFE_METHOD_CALL "length" argc=0
```

### 2.4 Null Safety Rules

**Type Compatibility:**
- `string?` can hold `string` or `null`
- `string` cannot hold `null`
- Automatic null checks at assignment

**VM Validation:**
```dart
bool _isTypeCompatible(dynamic value, String expectedType) {
    if (value == null) return expectedType.endsWith('?');
    // ... other checks
}
```

## 3. Traits Processing

### 3.1 Trait Definition
```fluent
interface Drawable {
    fun draw(): void;
}
```

**Compilation:**
```
DEFINE_INTERFACE {
    name: "Drawable",
    methods: [
        {name: "draw", params: [], returnType: "void"}
    ]
}
```

### 3.2 Trait Implementation
```fluent
Circle: Drawable {
    fun draw(): void {
        print("Drawing circle");
    }
}
```

**Compilation:**
```
IMPL_TRAIT {
    struct: "Circle",
    interface: "Drawable",
    methods: {
        "draw": [compiled_bytecode]
    }
}
```

### 3.3 Trait Bounds với Generics
```fluent
struct Container<T: Drawable> {
    item: T;
}
```

**Processing Steps:**
1. Parse trait bound: `T: Drawable`
2. Store constraint: `{"T": ["Drawable"]}`
3. Validate at instantiation: `Container<Circle>`
4. Check `Circle` implements `Drawable`

### 3.4 Runtime Trait Resolution

**Method Call Resolution:**
```fluent
container.item.draw();
```

**VM Steps:**
1. Load `container.item` (type `T`)
2. Resolve concrete type (e.g., `Circle`)
3. Lookup trait implementation: `Circle: Drawable`
4. Execute `draw` method

**OpCode:**
```
RESOLVE_GENERIC_METHOD {
    method: "draw",
    argc: 0,
    typeArguments: {"T": "Circle"}
}
```

## 4. Advanced Features

### 4.1 Multiple Trait Bounds
```fluent
struct Processor<T: Drawable & Serializable> {
    process(item: T): void;
}
```

**Validation:**
- Check `T` implements both `Drawable` AND `Serializable`
- Store multiple constraints: `{"T": ["Drawable", "Serializable"]}`

### 4.2 Generic Method Resolution

**VM Algorithm:**
1. Extract type arguments from instance
2. Find matching method signature
3. Substitute type parameters
4. Execute specialized bytecode

### 4.3 Type Erasure vs Monomorphization

**Current Approach: Partial Monomorphization**
- Store generic definitions
- Generate specialized instances at runtime
- Cache concrete type mappings

## 5. Error Handling

### 5.1 Type Errors
- **Null Assignment**: `string x = null;` → Exception
- **Trait Bound Violation**: `Container<int>` where `T: Drawable` → Exception
- **Generic Arity Mismatch**: `Box<int, string>` for `Box<T>` → Exception

### 5.2 Runtime Checks
- Null checks before field access
- Trait bound validation at instantiation
- Type compatibility at assignment

## 6. Performance Considerations

### 6.1 Type Registry Caching
- Cache generic type definitions
- Reuse concrete type instances
- Optimize method lookup

### 6.2 Lazy Evaluation
- Generate concrete types on-demand
- Cache method resolutions
- Minimize runtime type checks

## 7. Future Enhancements

### 7.1 Planned Features
- Higher-kinded types: `Container<Container<T>>`
- Associated types: `trait Iterator { type Item; }`
- Generic constraints: `where T: Clone + Eq`

### 7.2 Optimization Opportunities
- Compile-time monomorphization
- Static dispatch for known types
- Inline generic method calls
