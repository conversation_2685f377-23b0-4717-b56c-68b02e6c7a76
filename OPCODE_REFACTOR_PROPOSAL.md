# OpCode Refactor Proposal: Type-Safe Sealed Classes

## Vấn đề hiện tại

### 1. Type Safety Issues
```dart
// Hiện tại - không type safe
final instr = Instruction(OpCode.LOAD_CONST, [42]);
final instr2 = Instruction(OpCode.METHOD_CALL, [{'method': 'test', 'argc': 2}]);

// <PERSON><PERSON> thể gây lỗi runtime
final value = args[0]; // Không biết type gì
final methodName = (args[0] as Map<String, dynamic>)['method']; // Phức tạp
```

### 2. Khó Debug và Maintain
```dart
// Trong VM switch case
case OpCode.METHOD_CALL:
  final data = args[0] as Map<String, dynamic>; // Phải cast
  final methodName = data['method'] as String;   // Phải cast
  final argc = data['argc'] as int;              // Phải cast
```

## Gi<PERSON>i pháp: Sealed Class OpCode

### 1. Type-Safe OpCode Definitions
```dart
// Mới - type safe
sealed class OpCode {
  const OpCode();
}

class LoadConstOp extends OpCode {
  final dynamic value;
  const LoadConstOp(this.value);
}

class MethodCallOp extends OpCode {
  final String method;
  final int argc;
  const MethodCallOp(this.method, this.argc);
}

class StaticCallOp extends OpCode {
  final String structName;
  final String method;
  final int argc;
  const StaticCallOp(this.structName, this.method, this.argc);
}
```

### 2. Simplified Instruction
```dart
class Instruction {
  final OpCode op;
  const Instruction(this.op);
}
```

## So sánh Before/After

### Compiler Changes

#### Before:
```dart
void _emit(OpCode op, [dynamic arg1, dynamic arg2]) {
  final args = <dynamic>[];
  if (arg1 != null) args.add(arg1);
  if (arg2 != null) args.add(arg2);
  _instructions.add(Instruction(op, args));
}

// Usage
_emit(OpCode.LOAD_CONST, 42);
_emit(OpCode.METHOD_CALL, {
  'method': methodName,
  'argc': argc,
});
```

#### After:
```dart
void emit(OpCode op) {
  _instructions.add(Instruction(op));
}

// Usage - type safe!
emit(LoadConstOp(42));
emit(MethodCallOp(methodName, argc));
emit(StaticCallOp(structName, methodName, argc));
```

### Virtual Machine Changes

#### Before:
```dart
switch (op) {
  case OpCode.LOAD_CONST:
    stack.add(args[0]); // args[0] có thể là gì?
    break;
    
  case OpCode.METHOD_CALL:
    final data = args[0] as Map<String, dynamic>; // Phải cast
    final methodName = data['method'] as String;
    final argc = data['argc'] as int;
    // ... xử lý
    break;
    
  case OpCode.STATIC_CALL:
    final data = args[0] as Map<String, dynamic>;
    final struct = data['struct'] as String;
    final method = data['method'] as String;
    final argc = data['argc'] as int;
    // ... xử lý
    break;
}
```

#### After:
```dart
switch (instr.op) {
  case LoadConstOp(value: final value):
    stack.add(value); // Type safe!
    break;
    
  case MethodCallOp(method: final methodName, argc: final argc):
    // Direct access to typed fields
    _executeMethodCall(methodName, argc);
    break;
    
  case StaticCallOp(structName: final struct, method: final method, argc: final argc):
    // All fields are typed and accessible
    _executeStaticCall(struct, method, argc);
    break;
}
```

## Lợi ích

### 1. Type Safety
- ✅ Compile-time type checking
- ✅ IDE autocomplete và IntelliSense
- ✅ Không cần cast runtime

### 2. Code Clarity
- ✅ Rõ ràng fields nào cần cho mỗi OpCode
- ✅ Self-documenting code
- ✅ Dễ đọc và hiểu

### 3. Maintainability
- ✅ Refactoring an toàn hơn
- ✅ Thêm fields mới dễ dàng
- ✅ Compiler sẽ báo lỗi nếu thiếu case

### 4. Performance
- ✅ Không cần Map lookups
- ✅ Không cần runtime casting
- ✅ Better memory layout

## Migration Strategy

### Phase 1: Create New System
1. ✅ Tạo `instruction_new.dart` với sealed classes
2. ✅ Define tất cả OpCode variants
3. ✅ Test với một số OpCode đơn giản

### Phase 2: Gradual Migration
1. Migrate Compiler để emit new OpCodes
2. Migrate VM để handle new OpCodes
3. Update tests từng phần

### Phase 3: Complete Transition
1. Remove old OpCode enum
2. Remove old Instruction class
3. Rename `instruction_new.dart` → `instruction.dart`

## Example: Complex OpCode

### Generic Method Call
```dart
// Before
_emit(OpCode.RESOLVE_GENERIC_METHOD, {
  'method': methodName,
  'argc': argc,
  'typeArguments': {'T': 'int', 'U': 'string'}
});

// After
emit(ResolveGenericMethodOp(
  methodName, 
  argc, 
  {'T': 'int', 'U': 'string'}
));
```

### VM Handling
```dart
// Before
case OpCode.RESOLVE_GENERIC_METHOD:
  final data = args[0] as Map<String, dynamic>;
  final methodName = data['method'] as String;
  final argc = data['argc'] as int;
  final typeArgs = Map<String, String>.from(data['typeArguments'] ?? {});

// After  
case ResolveGenericMethodOp(
  method: final methodName,
  argc: final argc, 
  typeArguments: final typeArgs
):
  // Direct access, no casting needed!
```

## Kết luận

Refactor này sẽ làm cho codebase:
- **An toàn hơn** với compile-time type checking
- **Dễ đọc hơn** với self-documenting OpCodes  
- **Dễ maintain hơn** với pattern matching
- **Hiệu suất tốt hơn** với direct field access

Đây là một improvement đáng kể cho architecture của Fluent Lang VM.
