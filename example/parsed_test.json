[{"type": "struct", "name": {"type": "identifier", "name": "Todo"}, "fields": [{"name": "id", "type": "identifier"}, {"name": "title", "type": "identifier"}, {"name": "completed", "type": "identifier"}]}, {"type": "interface", "name": {"type": "identifier", "name": "ITodo"}, "methods": [{"name": {"type": "identifier", "name": "toggle"}, "params": [], "returnType": {"kind": "named", "name": "void"}}]}, {"type": "impl", "structName": {"type": "identifier", "name": "Todo"}, "traitName": {"type": "identifier", "name": "ITodo"}, "methods": [{"type": "function", "static": true, "name": {"type": "identifier", "name": "new"}, "params": [{"name": {"type": "identifier", "name": "id"}, "type": {"kind": "named", "name": "num"}}, {"name": {"type": "identifier", "name": "title"}, "type": {"kind": "named", "name": "string"}}, {"name": {"type": "identifier", "name": "completed"}, "type": {"kind": "named", "name": "bool"}}], "returnType": {"kind": "named", "name": "Todo"}, "body": [{"type": "return", "value": {"type": "struct_lit", "name": "Todo", "fields": [{"name": "id", "value": {"type": "identifier", "name": "id"}}, {"name": "title", "value": {"type": "identifier", "name": "title"}}, {"name": "completed", "value": {"type": "identifier", "name": "completed"}}]}}]}, {"type": "function", "static": false, "name": {"type": "identifier", "name": "toggle"}, "params": [], "returnType": {"kind": "named", "name": "void"}, "body": [{"type": "assign", "target": {"type": "field_access", "object": {"type": "this"}, "field": "completed"}, "value": {"type": "unary", "op": "!", "expr": {"type": "field_access", "object": {"type": "this"}, "field": "completed"}}}]}]}, {"type": "function", "static": false, "name": {"type": "identifier", "name": "main"}, "params": [], "returnType": "void", "body": [{"type": "let", "names": [{"type": "identifier", "name": "todo"}], "values": [{"type": "static_call", "struct": "Todo", "method": "new", "args": [{"type": "number", "value": 1}, {"type": "string", "value": "Learn Fluent"}, {"type": "boolean", "value": false}]}]}, {"type": "call_expr", "callee": {"type": "field_access", "object": {"type": "identifier", "name": "todo"}, "field": "toggle"}, "args": []}, {"type": "print", "value": {"type": "field_access", "object": {"type": "identifier", "name": "todo"}, "field": "completed"}}, {"type": "call_expr", "callee": {"type": "field_access", "object": {"type": "identifier", "name": "todo"}, "field": "toggle"}, "args": []}, {"type": "print", "value": {"type": "field_access", "object": {"type": "identifier", "name": "todo"}, "field": "completed"}}]}, {"type": "call", "name": {"type": "identifier", "name": "main"}, "args": []}]