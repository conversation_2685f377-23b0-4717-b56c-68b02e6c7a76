struct Todo {
  id: num;
  title: string;
  completed: bool;
}

interface ITodo {
  fun toggle(): void;
}

Todo: ITodo {
  static fun new(id: num, title: string, completed: bool): Todo {
    return Todo { id: id, title: title, completed: completed };
  }

  fun toggle():void{
    this.completed = !this.completed;
  }
}


fun main() {
  let todo = Todo::new(1, "Learn Fluent", false);
  todo.toggle();
  print(todo.completed);
  todo.toggle();
  print(todo.completed);
}

main();