import 'dart:convert';
import 'dart:io';
import 'package:args/args.dart';
import 'package:petitparser/petitparser.dart';
import 'core/core.dart';

const String version = '0.0.1';

ArgParser buildArgParser() {
  return ArgParser()
    ..addFlag(
      'help',
      abbr: 'h',
      negatable: false,
      help: 'Print this usage information.',
    )
    ..addFlag(
      'verbose',
      abbr: 'v',
      negatable: false,
      help: 'Show additional command output.',
    )
    ..addFlag('version', negatable: false, help: 'Print the tool version.')
    ..addCommand('run')
    ..addCommand('build')
    ..addCommand('ast'); // 👈 thêm dòng này
}

void dumpAstFile(String path) {
  final code = File(path).readAsStringSync();
  final parser = buildParser(verbose: true);
  final result = parser.parse(code);

  if (result is Success) {
    final rawAst = result.value;
    final file = File(path.replaceFirst('.fl', '.ast.json'));
    file.writeAsStringSync(rawAst.toString());

  } else {
    print('[ERROR] ${result.message} at position ${result.toPositionString()}');
  }
}

void printUsage(ArgParser argParser) {
  print('Usage: dart fluent_lang.dart <command> [file] [flags]');
  print(argParser.usage);
}

void startRepl({bool verbose = false}) {
  final parser = buildParser(verbose: verbose);
  print('FluentLang REPL v$version');
  print('Type "exit" to quit.\n');

  final compiler = Compiler();
  compiler.verbose = verbose;
  final vm = VirtualMachine();

  while (true) {
    stdout.write('> ');
    final line = stdin.readLineSync();

    if (line == null || line.trim() == 'exit') {
      print('Goodbye!');
      break;
    }

    final result = parser.parse(line);

    if (result is Success) {
      // final astList = (result.value as List).map(convertToAST).toList();
      compiler.compile(result.value);
      final bytecode = compiler.getBytecode();
      final resolved = resolveLabels(bytecode);
      vm.load(resolved);
      vm.run();
    } else {
      print(
        '[ERROR] ${result.message} at position ${result.toPositionString()}',
      );
    }

    if (verbose) {
      print('[VERBOSE] raw input: "$line"');
    }
  }
}

void runFile(String path, {bool verbose = false}) {
  final code = File(path).readAsStringSync();
  final parser = buildParser(verbose: verbose);
  final result = parser.parse(code);

  if (result is Success) {
    final compiler = Compiler();
    compiler.verbose = verbose;
    compiler.compile(result.value);
    final bytecode = compiler.getBytecode();

    final vm = VirtualMachine();
    vm.verbose = verbose;
    vm.load(bytecode);
    if(verbose) {
      for (var instr in bytecode.instructions) {
        print(instr);
      }
    }
    vm.run();
  } else {
    print('[ERROR] ${result.message} at position ${result.toPositionString()}');
  }

  if (verbose) {
    print('[VERBOSE] Finished running $path');
  }
}

void buildFile(String path, String outputPath) {
  final code = File(path).readAsStringSync();
  final parser = buildParser();
  final result = parser.parse(code);

  if (result is Success) {
    final program = result.value as Program;
    final compiler = Compiler();
    compiler.compile(program);
    final bytecode = compiler.getBytecode();
    final outputFile = File(outputPath);
    outputFile.writeAsStringSync(
      bytecode.instructions.map((i) => i.toString()).join('\n'),
    );
    print('Build successful: $outputPath');
  } else {
    print('[ERROR] ${result.message} at position ${result.toPositionString()}');
  }
}

void main(List<String> arguments) {
  final ArgParser argParser = buildArgParser();
  try {
    final ArgResults results = argParser.parse(arguments);
    bool verbose = results['verbose'] == true;

    if (results['help'] == true) {
      printUsage(argParser);
      return;
    }
    if (results['version'] == true) {
      print('fluent_lang version: $version');
      return;
    }

    if (results.command != null) {
      final command = results.command!.name;
      final args = results.command!.rest;

      switch (command) {
        case 'run':
          if (args.isEmpty) {
            print('[ERROR] No input file for run');
            return;
          }
          runFile(args[0], verbose: verbose);
          break;
        case 'build':
          if (args.length < 2) {
            print('[ERROR] Usage: fluent build <input> <output>');
            return;
          }
          buildFile(args[0], args[1]);
          break;
        case 'ast':
          if (args.isEmpty) {
            print('[ERROR] No input file for ast');
            return;
          }
          dumpAstFile(args[0]); // 👈 gọi hàm vừa thêm
          break;
        default:
          print('[ERROR] Unknown command: $command');
      }
    } else {
      startRepl(verbose: verbose);
    }
  } on FormatException catch (e) {
    print(e.message);
    print('');
    printUsage(argParser);
  }
}
