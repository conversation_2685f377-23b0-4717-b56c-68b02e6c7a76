// New type-safe instruction system using sealed classes

sealed class OpCode {
  const OpCode();
}

// === Basic Operations ===
class LoadConstOp extends OpCode {
  final dynamic value;
  const LoadConstOp(this.value);

  @override
  String toString() => 'LOAD_CONST $value';
}

class LoadOp extends OpCode {
  final String name;
  const LoadOp(this.name);

  @override
  String toString() => 'LOAD $name';
}

class StoreOp extends OpCode {
  final String name;
  const StoreOp(this.name);

  @override
  String toString() => 'STORE $name';
}

class LoadThisOp extends OpCode {
  const LoadThisOp();

  @override
  String toString() => 'LOAD_THIS';
}

// === Arithmetic Operations ===
class AddOp extends OpCode {
  const AddOp();

  @override
  String toString() => 'ADD';
}

class SubOp extends OpCode {
  const SubOp();

  @override
  String toString() => 'SUB';
}

class MulOp extends OpCode {
  const MulOp();

  @override
  String toString() => 'MUL';
}

class DivOp extends OpCode {
  const DivOp();

  @override
  String toString() => 'DIV';
}

class ModOp extends OpCode {
  const ModOp();

  @override
  String toString() => 'MOD';
}

// === Comparison Operations ===
class LessOp extends OpCode {
  const LessOp();

  @override
  String toString() => 'LESS';
}

class GreaterOp extends OpCode {
  const GreaterOp();

  @override
  String toString() => 'GREATER';
}

class EqOp extends OpCode {
  const EqOp();

  @override
  String toString() => 'EQ';
}

class NeqOp extends OpCode {
  const NeqOp();

  @override
  String toString() => 'NEQ';
}

class LeqOp extends OpCode {
  const LeqOp();

  @override
  String toString() => 'LEQ';
}

class GeqOp extends OpCode {
  const GeqOp();

  @override
  String toString() => 'GEQ';
}

// === Logical Operations ===
class NotOp extends OpCode {
  const NotOp();

  @override
  String toString() => 'NOT';
}

class NegOp extends OpCode {
  const NegOp();

  @override
  String toString() => 'NEG';
}

class PosOp extends OpCode {
  const PosOp();

  @override
  String toString() => 'POS';
}

// === Control Flow ===
class JumpOp extends OpCode {
  final String target;
  const JumpOp(this.target);

  @override
  String toString() => 'JUMP $target';
}

class JumpIfTrueOp extends OpCode {
  final String target;
  const JumpIfTrueOp(this.target);

  @override
  String toString() => 'JUMP_IF_TRUE $target';
}

class JumpIfFalseOp extends OpCode {
  final String target;
  const JumpIfFalseOp(this.target);

  @override
  String toString() => 'JUMP_IF_FALSE $target';
}

class LabelOp extends OpCode {
  final String name;
  const LabelOp(this.name);

  @override
  String toString() => 'LABEL $name';
}

class ReturnOp extends OpCode {
  const ReturnOp();

  @override
  String toString() => 'RETURN';
}

class CallOp extends OpCode {
  final String name;
  final int argc;
  const CallOp(this.name, this.argc);

  @override
  String toString() => 'CALL $name $argc';
}



class MethodCallOp extends OpCode {
  final String method;
  final int argc;
  const MethodCallOp(this.method, this.argc);

  @override
  String toString() => 'METHOD_CALL $method $argc';
}

class StaticCallOp extends OpCode {
  final String structName;
  final String method;
  final int argc;
  const StaticCallOp(this.structName, this.method, this.argc);

  @override
  String toString() => 'STATIC_CALL $structName::$method $argc';
}

// === Struct Operations ===
class DefineStructOp extends OpCode {
  final String name;
  final Map<String, String> fields;
  const DefineStructOp(this.name, this.fields);

  @override
  String toString() => 'DEFINE_STRUCT $name $fields';
}

class MakeStructOp extends OpCode {
  final String structName;
  final List<String> fieldNames;
  const MakeStructOp(this.structName, this.fieldNames);

  @override
  String toString() => 'MAKE_STRUCT $structName $fieldNames';
}

// === Enhanced Struct Operations ===
class DefineGenericStructOp extends OpCode {
  final String name;
  final List<String> typeParameters;
  final Map<String, String> fields;
  final Map<String, List<String>>? parameterBounds;

  const DefineGenericStructOp(
    this.name,
    this.typeParameters,
    this.fields,
    {this.parameterBounds}
  );

  @override
  String toString() => 'DEFINE_GENERIC_STRUCT $name<${typeParameters.join(', ')}> $fields';
}

class InstantiateGenericOp extends OpCode {
  final String baseName;
  final Map<String, String> typeArguments;
  final List<String> fieldNames;

  const InstantiateGenericOp(this.baseName, this.typeArguments, this.fieldNames);

  @override
  String toString() => 'INSTANTIATE_GENERIC $baseName<${typeArguments.values.join(', ')}> $fieldNames';
}

class GetFieldOp extends OpCode {
  final String field;
  const GetFieldOp(this.field);

  @override
  String toString() => 'GET_FIELD $field';
}

class SetFieldOp extends OpCode {
  final String field;
  const SetFieldOp(this.field);

  @override
  String toString() => 'SET_FIELD $field';
}

class LoadThisDupOp extends OpCode {
  const LoadThisDupOp();

  @override
  String toString() => 'LOAD_THIS_DUP';
}

// Duplicate method call operations removed - already defined above

// === Field Assignment Operations ===
class FieldAssignOp extends OpCode {
  final String field;
  const FieldAssignOp(this.field);

  @override
  String toString() => 'FIELD_ASSIGN $field';
}

// === Interface/Trait Operations ===
class DefineInterfaceOp extends OpCode {
  final String name;
  final List<Map<String, dynamic>> methods;
  const DefineInterfaceOp(this.name, this.methods);

  @override
  String toString() => 'DEFINE_INTERFACE $name';
}

class ImplTraitOp extends OpCode {
  final String structName;
  final String interfaceName;
  final Map<String, dynamic> methods; // Store as dynamic to avoid type conflicts
  final Map<String, dynamic> staticMethods;
  const ImplTraitOp(this.structName, this.interfaceName, this.methods, this.staticMethods);

  @override
  String toString() => 'IMPL_TRAIT $structName : $interfaceName';
}

// Duplicate classes removed - already defined above

// === Utility Operations ===
class PrintOp extends OpCode {
  const PrintOp();

  @override
  String toString() => 'PRINT';
}

class TypeCheckOp extends OpCode {
  final String expectedType;
  const TypeCheckOp(this.expectedType);

  @override
  String toString() => 'TYPE_CHECK $expectedType';
}

// === Nullable Operations ===
class NullCheckOp extends OpCode {
  const NullCheckOp();

  @override
  String toString() => 'NULL_CHECK';
}

class SafeGetFieldOp extends OpCode {
  final String field;
  const SafeGetFieldOp(this.field);

  @override
  String toString() => 'SAFE_GET_FIELD $field';
}

class SafeMethodCallOp extends OpCode {
  final String method;
  final int argc;
  const SafeMethodCallOp(this.method, this.argc);

  @override
  String toString() => 'SAFE_METHOD_CALL $method $argc';
}

// === Enhanced Trait Operations ===
class CheckTraitBoundOp extends OpCode {
  final String traitName;
  const CheckTraitBoundOp(this.traitName);

  @override
  String toString() => 'CHECK_TRAIT_BOUND $traitName';
}

class ResolveGenericMethodOp extends OpCode {
  final String method;
  final int argc;
  final Map<String, String> typeArguments;
  const ResolveGenericMethodOp(this.method, this.argc, this.typeArguments);

  @override
  String toString() => 'RESOLVE_GENERIC_METHOD $method $argc ${typeArguments}';
}

// === Enum Operations ===
class DefineEnumOp extends OpCode {
  final String name;
  final Map<String, List<String>> variants;
  const DefineEnumOp(this.name, this.variants);

  @override
  String toString() => 'DEFINE_ENUM $name';
}

class MakeEnumVariantOp extends OpCode {
  final String enumName;
  final String variant;
  final int valueCount;
  const MakeEnumVariantOp(this.enumName, this.variant, this.valueCount);

  @override
  String toString() => 'MAKE_ENUM_VARIANT $enumName::$variant($valueCount)';
}

// === Map Operations ===
class MakeMapOp extends OpCode {
  final String keyType;
  final String valueType;
  const MakeMapOp(this.keyType, this.valueType);

  @override
  String toString() => 'MAKE_MAP<$keyType, $valueType>';
}

class MapGetOp extends OpCode {
  const MapGetOp();

  @override
  String toString() => 'MAP_GET';
}

class MapSetOp extends OpCode {
  const MapSetOp();

  @override
  String toString() => 'MAP_SET';
}

class MapContainsKeyOp extends OpCode {
  const MapContainsKeyOp();

  @override
  String toString() => 'MAP_CONTAINS_KEY';
}

// === Array Operations ===
class ArrayGetOp extends OpCode {
  const ArrayGetOp();

  @override
  String toString() => 'ARRAY_GET';
}

class ArraySetOp extends OpCode {
  const ArraySetOp();

  @override
  String toString() => 'ARRAY_SET';
}

// === Error Handling ===
class ThrowOp extends OpCode {
  const ThrowOp();

  @override
  String toString() => 'THROW';
}

class TryBeginOp extends OpCode {
  final int catchPC;
  final int? finallyPC;
  const TryBeginOp(this.catchPC, {this.finallyPC});

  @override
  String toString() => 'TRY_BEGIN catch:$catchPC${finallyPC != null ? ' finally:$finallyPC' : ''}';
}

class TryEndOp extends OpCode {
  const TryEndOp();

  @override
  String toString() => 'TRY_END';
}

class CatchBeginOp extends OpCode {
  final String? exceptionType;
  const CatchBeginOp({this.exceptionType});

  @override
  String toString() => 'CATCH_BEGIN${exceptionType != null ? ' $exceptionType' : ''}';
}

class CatchEndOp extends OpCode {
  const CatchEndOp();

  @override
  String toString() => 'CATCH_END';
}

// Duplicate classes removed - already defined above

// Instruction class is now in instruction.dart
