import 'dart:io';
import 'package:meta/meta.dart';
import 'package:fluent_lang/core/ast.dart';

import 'instruction.dart';
import 'instruction_new.dart' as new_instr;
import 'bytecode.dart';
import 'runtime.dart';

class _ReturnException implements Exception {
  final dynamic value;

  _ReturnException(this.value);
}

class _MethodReference {
  final StructInstance instance;
  final String methodName;

  _MethodReference(this.instance, this.methodName);

  @override
  String toString() => '${instance.typeName}.${methodName}';
}

class _TryBlock {
  final int startPC;
  final List<_CatchHandler> catchHandlers;
  final int? finallyPC;

  _TryBlock(this.startPC, this.catchHandlers, {this.finallyPC});
}

class _CatchHandler {
  final String? exceptionType;
  final int handlerPC;

  _CatchHandler(this.handlerPC, {this.exceptionType});
}

class _FluentException implements Exception {
  final dynamic value;

  _FluentException(this.value);

  @override
  String toString() => 'FluentException: $value';
}



class VirtualMachine {
  bool _verbose = false;

  set verbose(bool value) => _verbose = value;

  List<dynamic> stack = [];
  Map<String, dynamic> vars = {};
  List<Instruction> code = [];
  int ip = 0;

  StructInstance? thisInstance;

  // Centralized type registry
  TypeRegistry typeRegistry = TypeRegistry();

  // Error handling
  List<_TryBlock> tryStack = []; // Stack of try blocks for error handling

  // Backward compatibility getters for tests
  @visibleForTesting
  Map<String, Map<String, List<Instruction>>> get staticTraitImpls => typeRegistry.staticTraitImpls;

  void load(Bytecode bytecode) {
    code = bytecode.instructions;
    ip = 0;
    stack.clear();
    vars.clear();
  }

  void run() {
    if (_verbose) print("Running VM with code: $code");
    while (ip < code.length) {
      final instr = code[ip++];
      _executeInstruction(instr.op);
    }
  }

  // Execute type-safe instructions
  void _executeInstruction(new_instr.OpCode op) {
    switch (op) {
      case new_instr.LoadConstOp(value: final value):
        stack.add(value);
        break;

      case new_instr.LoadOp(name: final name):
        if (name == 'null') {
          stack.add(null);
        } else if (vars.containsKey(name)) {
          stack.add(vars[name]);
        } else {
          throw Exception('Variable "$name" not defined');
        }
        break;

      case new_instr.StoreOp(name: final name):
        vars[name] = stack.removeLast();
        break;

      case new_instr.LoadThisOp():
        final thisInstance = vars['this'];
        if (thisInstance == null) {
          throw Exception('No "this" context in current scope');
        }
        stack.add(thisInstance);
        break;

      case new_instr.AddOp():
        final b = stack.removeLast();
        final a = stack.removeLast();
        stack.add(a + b);
        break;

      case new_instr.SubOp():
        final b = stack.removeLast();
        final a = stack.removeLast();
        stack.add(a - b);
        break;

      case new_instr.MulOp():
        final b = stack.removeLast();
        final a = stack.removeLast();
        stack.add(a * b);
        break;

      case new_instr.DivOp():
        final b = stack.removeLast();
        final a = stack.removeLast();
        stack.add(a ~/ b);
        break;

      case new_instr.ModOp():
        final b = stack.removeLast();
        final a = stack.removeLast();
        stack.add(a % b);
        break;

      case new_instr.LessOp():
        final b = stack.removeLast();
        final a = stack.removeLast();
        stack.add(a < b);
        break;

      case new_instr.GreaterOp():
        final b = stack.removeLast();
        final a = stack.removeLast();
        stack.add(a > b);
        break;

      case new_instr.EqOp():
        final b = stack.removeLast();
        final a = stack.removeLast();
        stack.add(a == b);
        break;

      case new_instr.NeqOp():
        final b = stack.removeLast();
        final a = stack.removeLast();
        stack.add(a != b);
        break;

      case new_instr.LeqOp():
        final b = stack.removeLast();
        final a = stack.removeLast();
        stack.add(a <= b);
        break;

      case new_instr.GeqOp():
        final b = stack.removeLast();
        final a = stack.removeLast();
        stack.add(a >= b);
        break;

      case new_instr.NotOp():
        final value = stack.removeLast();
        if (value is! bool) {
          throw Exception(
            'OpCode.NOT expects bool, but got $value (${value.runtimeType})',
          );
        }
        stack.add(!value);
        break;

      case new_instr.NegOp():
        final value = stack.removeLast();
        stack.add(-value);
        break;

      case new_instr.PosOp():
        final value = stack.removeLast();
        stack.add(value);
        break;

      case new_instr.PrintOp():
        final value = stack.removeLast();
        if (value is String && value.contains(r'${')) {
          final interpolated = _interpolate(value, vars);
          stdout.writeln(interpolated);
        } else {
          stdout.writeln(value);
        }
        break;

      case new_instr.ReturnOp():
        final returnValue = stack.isNotEmpty ? stack.removeLast() : null;
        stack.clear(); // clear toàn bộ stack khi return
        throw _ReturnException(returnValue);

      case new_instr.LabelOp(name: final name):
        // Labels are handled during bytecode resolution, no runtime action needed
        break;

      case new_instr.JumpOp(target: final target):
        // For resolved labels, target is already an index as string
        ip = int.parse(target);
        break;

      case new_instr.JumpIfTrueOp(target: final target):
        final condition = stack.removeLast();
        if (condition) {
          ip = int.parse(target);
        }
        break;

      case new_instr.JumpIfFalseOp(target: final target):
        final condition = stack.removeLast();
        if (!condition) {
          ip = int.parse(target);
        }
        break;

      case new_instr.CallOp(functionName: final name, argc: final argc):
        final func = vars[name];
        if (func is! Bytecode) {
          throw Exception('Cannot call non-function $name');
        }

        // Chuẩn bị máy ảo con để gọi hàm
        final vm = VirtualMachine();
        vm.verbose = _verbose;
        vm.load(func);

        // Copy biến toàn cục (nếu muốn chia sẻ)
        vm.vars = Map<String, dynamic>.from(vars);
        copyContextTo(vm);

        // Pop argument từ stack (ngược chiều push)
        final argsList = List.generate(argc, (_) => stack.removeLast()).reversed.toList();

        // Gán tham số vào biến cục bộ của hàm
        for (var i = 0; i < argc; i++) {
          vm.vars['arg$i'] = argsList[i];
        }

        try {
          vm.run();
          // Nếu không có return -> push null để stack đồng nhất
          stack.add(null);
        } on _ReturnException catch (e) {
          stack.add(e.value); // đưa return value vào stack gọi
        }
        break;

      default:
        throw Exception('Unhandled OpCode: ${op.runtimeType}. This OpCode has not been migrated to the new system yet.');
    }
  }

  // Legacy method - temporarily disabled
  void _executeLegacyInstruction(dynamic op, List<dynamic> args) {
    throw Exception('Legacy instruction system disabled. All instructions should use new OpCode system.');
    /*
    switch (op) {
      case OpCode.CALL:
          final name = args[0];
          final argc = args[1];
          final func = vars[name];

          if (func is! Bytecode) {
            throw Exception('Cannot call non-function $name');
          }

          // Chuẩn bị máy ảo con để gọi hàm
          final vm = VirtualMachine();
          vm.verbose = _verbose;
          vm.load(func);

          // Copy biến toàn cục (nếu muốn chia sẻ)
          vm.vars = Map<String, dynamic>.from(vars);
          copyContextTo(vm);

          // Pop argument từ stack (ngược chiều push)
          final argsList =
              List.generate(argc, (_) => stack.removeLast()).reversed.toList();

          // Gán tham số vào biến cục bộ của hàm
          for (var i = 0; i < argc; i++) {
            vm.vars['arg$i'] = argsList[i];
          }

          try {
            vm.run();
            // Nếu không có return -> push null để stack đồng nhất
            stack.add(null);
          } on _ReturnException catch (e) {
            stack.add(e.value); // đưa return value vào stack gọi
          }
          break;

        case OpCode.RETURN:
          final returnValue = stack.isNotEmpty ? stack.removeLast() : null;
          stack.clear(); // clear toàn bộ stack khi return
          throw _ReturnException(returnValue);

        case OpCode.NOT:
          final value = stack.removeLast();
          if (value is! bool) {
            throw Exception(
              'OpCode.NOT expects bool, but got $value (${value.runtimeType})',
            );
          }
          stack.add(!value);
          break;
        case OpCode.NEG:
          final value = stack.removeLast();
          stack.add(-value);
          break;
        case OpCode.POS:
          // Should do nothing?
          final value = stack.removeLast();
          stack.add(value);
          break;
        case OpCode.MOD:
          final b = stack.removeLast();
          final a = stack.removeLast();
          stack.add(a % b);
          break;
        case OpCode.LESS:
          final b = stack.removeLast();
          final a = stack.removeLast();
          stack.add(a < b);
          break;
        case OpCode.GREATER:
          final b = stack.removeLast();
          final a = stack.removeLast();
          stack.add(a > b);
          break;

        case OpCode.EQ:
          final b = stack.removeLast();
          final a = stack.removeLast();
          stack.add(a == b);
          break;

        case OpCode.NEQ:
          final b = stack.removeLast();
          final a = stack.removeLast();
          stack.add(a != b);
          break;

        case OpCode.LEQ:
          final b = stack.removeLast();
          final a = stack.removeLast();
          stack.add(a <= b);
          break;

        case OpCode.GEQ:
          final b = stack.removeLast();
          final a = stack.removeLast();
          stack.add(a >= b);
          break;
        case OpCode.JUMP_IF_TRUE:
          final condition = stack.removeLast();
          final target = args[0] as int;
          if (condition) {
            ip = target;
          }
          break;

        case OpCode.JUMP:
          final target = args[0] as int;
          ip = target;
          break;

        case OpCode.JUMP_IF_FALSE:
          final condition = stack.removeLast();
          final target = args[0] as int;
          if (!condition) {
            ip = target;
          }
          break;

        case OpCode.DEFINE_STRUCT:
          final data = args[0] as Map<String, dynamic>;
          final name = data['name'] as String;
          final fields = Map<String, String>.from(data['fields']);
          typeRegistry.registerStruct(name, fields);
          break;

        case OpCode.DEFINE_INTERFACE:
          final data = args[0] as Map<String, dynamic>;
          final name = data['name'] as String;
          final methods = List<Map<String, dynamic>>.from(data['methods']);
          typeRegistry.registerInterface(name, methods);
          break;

        case OpCode.IMPL_TRAIT:
          final data = args[0] as Map<String, dynamic>;
          final struct = data['struct'] as String;
          final trait = data['interface'] as String;

          final methodsRaw = Map<String, dynamic>.from(data['methods'] ?? {});
          final staticRaw = Map<String, dynamic>.from(data['static'] ?? {});

          // Instance methods
          final methodImplementations = <String, List<Instruction>>{};
          for (final entry in methodsRaw.entries) {
            final instrList = List<Map<String, dynamic>>.from(entry.value);
            final instructions =
                instrList
                    .map(
                      (i) => Instruction(
                        i['op'],
                        List<dynamic>.from(i['args'] ?? []),
                      ),
                    )
                    .toList();
            methodImplementations[entry.key] = instructions;
          }

          // Static methods
          final staticImplsForStruct = <String, List<Instruction>>{};
          for (final entry in staticRaw.entries) {
            final instrList = List<Map<String, dynamic>>.from(entry.value);
            final instructions =
                instrList
                    .map(
                      (i) => Instruction(
                        i['op'],
                        List<dynamic>.from(i['args'] ?? []),
                      ),
                    )
                    .toList();
            staticImplsForStruct[entry.key] = instructions;
          }

          typeRegistry.registerTraitImpls(struct, methodImplementations);
          typeRegistry.registerStaticTraitImpls(struct, staticImplsForStruct);

          if (_verbose) {
            print('[IMPL_TRAIT] Registered for struct $struct:');
            print('  Instance methods: ${methodImplementations.keys}');
            print('  Static methods: ${staticImplsForStruct.keys}');
          }

          break;

        case OpCode.MAKE_STRUCT:
          final structName = args[0] as String;
          final fieldNames = args[1] as List<String>;

          final fieldValues = <String, dynamic>{};
          for (int i = fieldNames.length - 1; i >= 0; i--) {
            fieldValues[fieldNames[i]] = stack.removeLast();
          }

          final instance = StructInstance(structName, fieldValues);
          stack.add(instance);
          break;
        case OpCode.GET_FIELD:
          final field = args[0] as String;
          final instance = stack.removeLast();
          if (instance is! StructInstance) {
            throw Exception(
              'GET_FIELD requires StructInstance but got $instance',
            );
          }

          // Check if this is actually a method call disguised as field access
          final structName = instance.typeName;
          final methodImpls = typeRegistry.getStructTraitImpls(structName);
          if (methodImpls != null && methodImpls.containsKey(field)) {
            // This is a method, create a method reference
            stack.add(_MethodReference(instance, field));
          } else {
            // This is a regular field
            stack.add(instance.getField(field));
          }
          break;

        case OpCode.SET_FIELD:
          // final data = args[0] as Map<String, dynamic>;
          final field = args[0] as String;
          final value = stack.removeLast();
          final instance = stack.removeLast();
          if (instance is! StructInstance) {
            throw Exception('SET_FIELD requires StructInstance');
          }
          instance.setField(field, value);
          break;

        case OpCode.METHOD_CALL:
          final data = args[0] as Map<String, dynamic>;
          final methodName = data['method'] as String;
          final argc = data['argc'] as int;

          final receiver = stack.removeAt(
            stack.length - argc - 1,
          ); // receiver nằm trước args

          StructInstance instance;
          String actualMethodName;

          if (receiver is _MethodReference) {
            // This is a method reference from GET_FIELD
            instance = receiver.instance;
            actualMethodName = receiver.methodName;
          } else if (receiver is StructInstance) {
            // Direct method call
            instance = receiver;
            actualMethodName = methodName;
          } else {
            throw Exception('Expected struct instance or method reference for method call, got $receiver');
          }

          final structName = instance.typeName;
          final methodCode = typeRegistry.getTraitImpl(structName, actualMethodName);
          if (methodCode == null) {
            throw Exception('Method $actualMethodName not found for $structName');
          }

          final vm = VirtualMachine();
          copyContextTo(vm);

          vm.load(Bytecode(methodCode));

          vm.thisInstance = instance;
          vm.vars['this'] = instance;

          final argsList =
              List.generate(argc, (_) => stack.removeLast()).reversed.toList();
          for (var i = 0; i < argc; i++) {
            vm.vars['arg$i'] = argsList[i];
          }

          try {
            vm.run();
            stack.add(null);
          } on _ReturnException catch (e) {
            stack.add(e.value);
          }
          break;

        case OpCode.STATIC_CALL:
          final data = args[0] as Map<String, dynamic>;
          final struct = data['struct'] as String;
          final method = data['method'] as String;
          final argc = data['argc'] as int;

          if (_verbose) {
            print('[STATIC_CALL] Looking for $struct::$method');
            print('  Available static impls: ${typeRegistry.staticTraitImpls.keys}');
            for (final entry in typeRegistry.staticTraitImpls.entries) {
              print('    ${entry.key}: ${entry.value.keys}');
            }
          }

          final methodCode = typeRegistry.getStaticTraitImpl(struct, method);
          if (methodCode == null) {
            throw Exception(
              'Static method $method not found on struct $struct',
            );
          }

          final vm = VirtualMachine();
          vm.verbose = _verbose;
          copyContextTo(vm);

          vm.load(Bytecode(methodCode));

          final argsList =
              List.generate(argc, (_) => stack.removeLast()).reversed.toList();
          for (var i = 0; i < argc; i++) {
            vm.vars['arg$i'] = argsList[i];
          }

          try {
            vm.run();
            stack.add(null);
          } on _ReturnException catch (e) {
            stack.add(e.value);
          }
          break;
        case OpCode.LOAD_THIS_DUP:
          if (thisInstance == null) {
            throw Exception('Cannot LOAD_THIS_DUP outside of method context');
          }
          stack.add(thisInstance!);
          break;

        // Generic support
        case OpCode.DEFINE_GENERIC_STRUCT:
          final data = args[0] as Map<String, dynamic>;
          final name = data['name'] as String;
          final typeParams = List<String>.from(data['typeParameters'] ?? []);
          final fields = Map<String, String>.from(data['fields'] ?? {});
          final bounds = data['parameterBounds'] as Map<String, List<String>>?;

          final definition = GenericTypeDefinition(name, typeParams, fields, parameterBounds: bounds);
          typeRegistry.registerGenericType(definition);
          break;

        case OpCode.INSTANTIATE_GENERIC:
          final data = args[0] as Map<String, dynamic>;
          final baseName = data['baseName'] as String;
          final typeArgs = Map<String, String>.from(data['typeArguments']);
          final fieldNames = List<String>.from(data['fieldNames']);

          final definition = typeRegistry.getGenericType(baseName);
          if (definition == null) {
            throw Exception('Generic type $baseName not found');
          }

          // Pop field values from stack in reverse order
          final fieldValues = <String, dynamic>{};
          for (int i = fieldNames.length - 1; i >= 0; i--) {
            fieldValues[fieldNames[i]] = stack.removeLast();
          }

          final instance = definition.instantiate(typeArgs, fieldValues);
          stack.add(instance);
          break;

        case OpCode.TYPE_CHECK:
          final expectedType = args[0] as String;
          final value = stack.removeLast();

          if (!_isTypeCompatible(value, expectedType)) {
            throw Exception('Type check failed: expected $expectedType, got ${_getValueType(value)}');
          }

          stack.add(value); // Put value back on stack
          break;

        // Nullable support
        case OpCode.NULL_CHECK:
          final value = stack.removeLast();
          if (value == null) {
            throw Exception('Null check failed: value is null');
          }
          stack.add(value);
          break;

        case OpCode.SAFE_GET_FIELD:
          final field = args[0] as String;
          final instance = stack.removeLast();

          if (instance == null) {
            stack.add(null);
          } else if (instance is StructInstance) {
            stack.add(instance.safeGetField(field));
          } else {
            throw Exception('SAFE_GET_FIELD requires StructInstance or null');
          }
          break;

        case OpCode.SAFE_METHOD_CALL:
          final data = args[0] as Map<String, dynamic>;
          final methodName = data['method'] as String;
          final argc = data['argc'] as int;

          final instance = stack.removeAt(stack.length - argc - 1);

          if (instance == null) {
            // Remove arguments from stack and push null
            for (int i = 0; i < argc; i++) {
              stack.removeLast();
            }
            stack.add(null);
          } else {
            // Put instance back and do normal method call
            stack.insert(stack.length - argc, instance);
            _executeMethodCall(methodName, argc);
          }
          break;

        // Enhanced trait support
        case OpCode.CHECK_TRAIT_BOUND:
          final traitName = args[0] as String;
          final value = stack.removeLast();

          if (value is StructInstance) {
            if (!value.satisfiesTraitBound(traitName)) {
              throw Exception('Type ${value.typeName} does not implement trait $traitName');
            }
          } else {
            throw Exception('Trait bound check requires StructInstance');
          }

          stack.add(value);
          break;

        case OpCode.RESOLVE_GENERIC_METHOD:
          final data = args[0] as Map<String, dynamic>;
          final methodName = data['method'] as String;
          final argc = data['argc'] as int;
          final typeArgs = Map<String, String>.from(data['typeArguments'] ?? {});

          final instance = stack.removeAt(stack.length - argc - 1);
          if (instance is! StructInstance) {
            throw Exception('Expected struct instance for generic method call');
          }

          _executeGenericMethodCall(instance, methodName, argc, typeArgs);
          break;

        // Enum support
        case OpCode.DEFINE_ENUM:
          final data = args[0] as Map<String, dynamic>;
          final name = data['name'] as String;
          final variants = Map<String, List<String>>.from(data['variants']);
          typeRegistry.registerEnum(name, variants);
          break;

        case OpCode.MAKE_ENUM_VARIANT:
          final data = args[0] as Map<String, dynamic>;
          final enumName = data['enumName'] as String;
          final variant = data['variant'] as String;
          final valueCount = data['valueCount'] as int;

          final values = valueCount > 0
              ? List.generate(valueCount, (_) => stack.removeLast()).reversed.toList()
              : null;

          stack.add(EnumInstance(enumName, variant, values: values));
          break;

        // Map support
        case OpCode.MAKE_MAP:
          final data = args[0] as Map<String, dynamic>;
          final keyType = data['keyType'] as String;
          final valueType = data['valueType'] as String;
          stack.add(MapInstance(keyType, valueType));
          break;

        case OpCode.MAP_GET:
          final key = stack.removeLast();
          final map = stack.removeLast();
          if (map is! MapInstance) {
            throw Exception('MAP_GET requires MapInstance');
          }
          stack.add(map.get(key));
          break;

        case OpCode.MAP_SET:
          final value = stack.removeLast();
          final key = stack.removeLast();
          final map = stack.removeLast();
          if (map is! MapInstance) {
            throw Exception('MAP_SET requires MapInstance');
          }
          map.set(key, value);
          stack.add(map); // Put map back on stack
          break;

        case OpCode.MAP_CONTAINS_KEY:
          final key = stack.removeLast();
          final map = stack.removeLast();
          if (map is! MapInstance) {
            throw Exception('MAP_CONTAINS_KEY requires MapInstance');
          }
          stack.add(map.containsKey(key));
          break;

        // Array indexing
        case OpCode.ARRAY_GET:
          final index = stack.removeLast() as int;
          final array = stack.removeLast();
          if (array is! StructInstance || array.typeName != 'Array') {
            throw Exception('ARRAY_GET requires Array instance');
          }
          // This would need to be implemented in Array's trait implementation
          throw Exception('Array indexing not yet implemented');

        case OpCode.ARRAY_SET:
          final value = stack.removeLast();
          final index = stack.removeLast() as int;
          final array = stack.removeLast();
          if (array is! StructInstance || array.typeName != 'Array') {
            throw Exception('ARRAY_SET requires Array instance');
          }
          // This would need to be implemented in Array's trait implementation
          throw Exception('Array indexing not yet implemented');

        // Error handling
        case OpCode.THROW:
          final exception = stack.removeLast();
          throw _FluentException(exception);

        default:
          throw Exception('Unknown op: $op');
      }
    }
    */
  }

  void copyContextTo(VirtualMachine vm) {
    typeRegistry.copyTo(vm.typeRegistry);
  }

  int _findLabelIndex(String labelName) {
    // This is a simplified implementation
    // In a real implementation, labels would be resolved during bytecode compilation
    // For now, we'll use the label name as a direct index (this is a hack for testing)
    try {
      return int.parse(labelName.replaceAll(RegExp(r'[^0-9]'), ''));
    } catch (e) {
      throw Exception('Label $labelName not found or invalid');
    }
  }

  // Execute new type-safe instructions
  void _executeNewInstruction(new_instr.OpCode op) {
    switch (op) {
      case new_instr.LoadConstOp(value: final value):
        stack.add(value);
        break;

      case new_instr.LoadOp(name: final name):
        if (name == 'null') {
          stack.add(null);
        } else if (vars.containsKey(name)) {
          stack.add(vars[name]);
        } else {
          throw Exception('Variable "$name" not defined');
        }
        break;

      case new_instr.StoreOp(name: final name):
        vars[name] = stack.removeLast();
        break;

      case new_instr.LoadThisOp():
        final thisInstance = vars['this'];
        if (thisInstance == null) {
          throw Exception('No "this" context in current scope');
        }
        stack.add(thisInstance);
        break;

      case new_instr.AddOp():
        final b = stack.removeLast();
        final a = stack.removeLast();
        stack.add(a + b);
        break;

      case new_instr.SubOp():
        final b = stack.removeLast();
        final a = stack.removeLast();
        stack.add(a - b);
        break;

      case new_instr.MulOp():
        final b = stack.removeLast();
        final a = stack.removeLast();
        stack.add(a * b);
        break;

      case new_instr.DivOp():
        final b = stack.removeLast();
        final a = stack.removeLast();
        stack.add(a ~/ b);
        break;

      case new_instr.ModOp():
        final b = stack.removeLast();
        final a = stack.removeLast();
        stack.add(a % b);
        break;

      case new_instr.LessOp():
        final b = stack.removeLast();
        final a = stack.removeLast();
        stack.add(a < b);
        break;

      case new_instr.GreaterOp():
        final b = stack.removeLast();
        final a = stack.removeLast();
        stack.add(a > b);
        break;

      case new_instr.EqOp():
        final b = stack.removeLast();
        final a = stack.removeLast();
        stack.add(a == b);
        break;

      case new_instr.NeqOp():
        final b = stack.removeLast();
        final a = stack.removeLast();
        stack.add(a != b);
        break;

      case new_instr.LeqOp():
        final b = stack.removeLast();
        final a = stack.removeLast();
        stack.add(a <= b);
        break;

      case new_instr.GeqOp():
        final b = stack.removeLast();
        final a = stack.removeLast();
        stack.add(a >= b);
        break;

      case new_instr.NotOp():
        final value = stack.removeLast();
        if (value is! bool) {
          throw Exception(
            'OpCode.NOT expects bool, but got $value (${value.runtimeType})',
          );
        }
        stack.add(!value);
        break;

      case new_instr.NegOp():
        final value = stack.removeLast();
        stack.add(-value);
        break;

      case new_instr.PosOp():
        final value = stack.removeLast();
        stack.add(value);
        break;

      case new_instr.PrintOp():
        final value = stack.removeLast();
        if (value is String && value.contains(r'${')) {
          final interpolated = _interpolate(value, vars);
          stdout.writeln(interpolated);
        } else {
          stdout.writeln(value);
        }
        break;

      case new_instr.ReturnOp():
        final returnValue = stack.isNotEmpty ? stack.removeLast() : null;
        stack.clear(); // clear toàn bộ stack khi return
        throw _ReturnException(returnValue);

      default:
        throw Exception('Unhandled new OpCode: ${op.runtimeType}');
    }
  }

  bool _isTypeCompatible(dynamic value, String expectedType) {
    if (expectedType == 'any' || expectedType == 'dynamic') return true;
    if (value == null) return expectedType.endsWith('?');

    final actualType = _getValueType(value);

    // Handle nullable types
    if (expectedType.endsWith('?')) {
      final baseType = expectedType.substring(0, expectedType.length - 1);
      return actualType == baseType;
    }

    // Handle generic types
    if (value is StructInstance && value.typeArguments != null) {
      return actualType == expectedType || value.getConcreteTypeName() == expectedType;
    }

    return actualType == expectedType;
  }

  String _getValueType(dynamic value) {
    if (value == null) return 'null';
    if (value is int) return 'num';
    if (value is double) return 'num';
    if (value is String) return 'string';
    if (value is bool) return 'bool';
    if (value is StructInstance) return value.getConcreteTypeName();
    return 'unknown';
  }

  void _executeMethodCall(String methodName, int argc) {
    final instance = stack.removeAt(stack.length - argc - 1);
    if (instance is! StructInstance) {
      throw Exception('Expected struct instance for method call');
    }

    final structName = instance.typeName;
    final methodCode = typeRegistry.getTraitImpl(structName, methodName);
    if (methodCode == null) {
      throw Exception('Method $methodName not found for $structName');
    }

    final vm = VirtualMachine();
    copyContextTo(vm);

    vm.load(Bytecode(methodCode));
    vm.thisInstance = instance;
    vm.vars['this'] = instance;

    final argsList = List.generate(argc, (_) => stack.removeLast()).reversed.toList();
    for (var i = 0; i < argc; i++) {
      vm.vars['arg$i'] = argsList[i];
    }

    try {
      vm.run();
      stack.add(null);
    } on _ReturnException catch (e) {
      stack.add(e.value);
    }
  }

  void _executeGenericMethodCall(StructInstance instance, String methodName, int argc, Map<String, String> typeArgs) {
    final structName = instance.typeName;

    // Try to find generic implementation
    final typeSignature = typeArgs.values.join(',');
    final methodCode = typeRegistry.getGenericTraitImpl(structName, methodName, typeSignature);

    if (methodCode != null) {
      final vm = VirtualMachine();
      copyContextTo(vm);

      vm.load(Bytecode(methodCode));
      vm.thisInstance = instance;
      vm.vars['this'] = instance;

      // Add type arguments to VM context
      for (final entry in typeArgs.entries) {
        vm.vars['type_${entry.key}'] = entry.value;
      }

      final argsList = List.generate(argc, (_) => stack.removeLast()).reversed.toList();
      for (var i = 0; i < argc; i++) {
        vm.vars['arg$i'] = argsList[i];
      }

      try {
        vm.run();
        stack.add(null);
      } on _ReturnException catch (e) {
        stack.add(e.value);
      }
      return;
    }

    // Fallback to regular method call
    stack.insert(stack.length - argc, instance);
    _executeMethodCall(methodName, argc);
  }

  @override
  String toString() {
    return 'VirtualMachine{stack: $stack, vars: $vars, ip: $ip, typeRegistry: $typeRegistry}';
  }

  String _interpolate(String template, Map<String, dynamic> scope) {
    return template.replaceAllMapped(RegExp(r'\$\{([^}]+)\}'), (match) {
      final expr = match.group(1)!;
      if (expr.startsWith('this.')) {
        final field = expr.substring(5);
        final thisInstance = scope['this'];
        if (thisInstance is StructInstance &&
            thisInstance.fields.containsKey(field)) {
          return thisInstance.fields[field].toString();
        }
      }
      return match.group(0)!; // fallback giữ nguyên
    });
  }
}
