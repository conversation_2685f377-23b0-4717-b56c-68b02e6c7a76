abstract class TypeAnnotation {
  static TypeAnnotation from(Map<String, dynamic> json) {
    switch (json['kind']) {
      case 'named':
        return NamedType(json['name']);
      case 'generic':
        return GenericType(
          json['base'],
          (json['args'] as List)
              .map<TypeAnnotation>((json) => TypeAnnotation.from(json))
              .toList(),
        );
      case 'union':
        return UnionType(
          (json['types'] as List)
              .map<TypeAnnotation>((json) => TypeAnnotation.from(json))
              .toList(),
        );
      case 'optional':
        return OptionalType(TypeAnnotation.from(json['base']));
      case 'trait_bound':
        return TraitBoundType(json['name'], List<String>.from(json['bounds']));
      case 'struct':
        return StructType(
          json['name'],
          Map.fromEntries(
            (json['fields'] as Map<String, dynamic>).entries.map(
              (e) => MapEntry(e.key, TypeAnnotation.from(e.value)),
            ),
          ),
        );
      case 'function':
        return FunctionType(
          (json['params'] as List)
              .map<TypeAnnotation>((json) => TypeAnnotation.from(json))
              .toList(),
          TypeAnnotation.from(json['return']),
        );

      default:
        throw Exception('Unknown type annotation: ${json['kind']}');
    }
  }
}

class NamedType extends TypeAnnotation {
  final String name; // num, string, etc.
  NamedType(this.name);
}

class OptionalType extends TypeAnnotation {
  final TypeAnnotation base;

  OptionalType(this.base);
}

class UnionType extends TypeAnnotation {
  final List<TypeAnnotation> options;

  UnionType(this.options);
}

class GenericType extends TypeAnnotation {
  final String base;
  final List<TypeAnnotation> args;

  GenericType(this.base, this.args);
}

class TraitBoundType extends TypeAnnotation {
  final String name; // T
  final List<String> traitBounds; // e.g., ["Clone", "Eq"]
  TraitBoundType(this.name, this.traitBounds);
}

class StructType extends TypeAnnotation {
  final String name;
  final Map<String, TypeAnnotation> fields;

  StructType(this.name, this.fields);
}

class FunctionType extends TypeAnnotation {
  final List<TypeAnnotation> params;
  final TypeAnnotation returnType;

  FunctionType(this.params, this.returnType);
}
