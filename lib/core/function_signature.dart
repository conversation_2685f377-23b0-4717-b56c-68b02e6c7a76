// function_signature.dart
import 'package:fluent_lang/core/ast.dart';

import 'type_annotation.dart';
class Parameter {
  final Identifier name;
  final TypeAnnotation type;

  Parameter(this.name, this.type);

  factory Parameter.from(Map<String, dynamic> json) {
    return Parameter(
      Identifier.from(json['name']),
      TypeAnnotation.from(json['type']),
    );
  }
}

class FunctionSignature {
  final String name;
  final List<Parameter> paramTypes;
  final TypeAnnotation returnType;

  FunctionSignature({
    required this.name,
    required this.paramTypes,
    required this.returnType,
  });
}
