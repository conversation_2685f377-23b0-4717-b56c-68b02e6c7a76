# Migration Plan: OpCode Sealed Classes

## Tổng quan

Kế hoạch migration từ hệ thống OpCode hiện tại (enum + dynamic args) sang sealed classes type-safe.

## Phase 1: Preparation ✅

### Completed:
- ✅ Tạo `lib/core/instruction_new.dart` với sealed class system
- ✅ Define tất cả OpCode variants với typed fields
- ✅ Tạo demo và verify functionality
- ✅ Document benefits và comparison

### Files created:
- `lib/core/instruction_new.dart` - New type-safe OpCode system
- `OPCODE_REFACTOR_PROPOSAL.md` - Detailed proposal
- `demo_new_opcode_system.dart` - Working demo

## Phase 2: Gradual Migration

### Step 1: Update Compiler (Estimated: 2-3 hours)

**File: `lib/core/compiler.dart`**

#### Current emit method:
```dart
void _emit(OpCode op, [dynamic arg1, dynamic arg2]) {
  final args = <dynamic>[];
  if (arg1 != null) args.add(arg1);
  if (arg2 != null) args.add(arg2);
  _instructions.add(Instruction(op, args));
}
```

#### New emit method:
```dart
void emit(OpCode op) {
  _instructions.add(Instruction(op));
}
```

#### Migration examples:
```dart
// Before → After
_emit(OpCode.LOAD_CONST, 42) → emit(LoadConstOp(42))
_emit(OpCode.STORE, "x") → emit(StoreOp("x"))
_emit(OpCode.METHOD_CALL, {'method': name, 'argc': argc}) → emit(MethodCallOp(name, argc))
```

### Step 2: Update Virtual Machine (Estimated: 4-5 hours)

**File: `lib/core/virtual_machine.dart`**

#### Current switch pattern:
```dart
switch (op) {
  case OpCode.LOAD_CONST:
    stack.add(args[0]);
    break;
}
```

#### New switch pattern:
```dart
switch (instr.op) {
  case LoadConstOp(value: final value):
    stack.add(value);
    break;
}
```

### Step 3: Update Tests (Estimated: 1-2 hours)

Update any tests that directly create Instructions or check OpCodes.

## Phase 3: Complete Migration

### Step 1: Remove Old System
- Remove old `OpCode` enum from `runtime.dart`
- Remove old `Instruction` class from `instruction.dart`

### Step 2: Rename Files
- `instruction_new.dart` → `instruction.dart`
- Update all imports

### Step 3: Final Cleanup
- Remove any remaining references to old system
- Update documentation

## Detailed Migration Steps

### Compiler Changes

#### Basic Operations:
```dart
// OLD
_emit(OpCode.LOAD_CONST, value);
_emit(OpCode.LOAD, name);
_emit(OpCode.STORE, name);
_emit(OpCode.ADD);

// NEW
emit(LoadConstOp(value));
emit(LoadOp(name));
emit(StoreOp(name));
emit(AddOp());
```

#### Complex Operations:
```dart
// OLD
_emit(OpCode.METHOD_CALL, {
  'method': methodName,
  'argc': argc,
});

// NEW
emit(MethodCallOp(methodName, argc));
```

#### Struct Operations:
```dart
// OLD
_emit(OpCode.DEFINE_STRUCT, {
  'name': structName,
  'fields': fields,
});

// NEW
emit(DefineStructOp(structName, fields));
```

### VM Changes

#### Pattern Matching Benefits:
```dart
// OLD - Error prone
case OpCode.STATIC_CALL:
  final data = args[0] as Map<String, dynamic>;
  final struct = data['struct'] as String;
  final method = data['method'] as String;
  final argc = data['argc'] as int;
  _executeStaticCall(struct, method, argc);

// NEW - Type safe
case StaticCallOp(
  structName: final struct,
  method: final method,
  argc: final argc
):
  _executeStaticCall(struct, method, argc);
```

## Risk Assessment

### Low Risk:
- ✅ Sealed classes are stable Dart feature
- ✅ Pattern matching is well-tested
- ✅ No runtime behavior changes
- ✅ Gradual migration possible

### Mitigation:
- Keep old system until migration complete
- Test each phase thoroughly
- Rollback plan available

## Benefits Recap

### Type Safety:
- Compile-time error detection
- No runtime casting needed
- IDE support with autocomplete

### Code Quality:
- Self-documenting OpCodes
- Cleaner switch statements
- Better maintainability

### Performance:
- No Map lookups
- Direct field access
- Better memory layout

## Timeline

### Week 1:
- [ ] Migrate Compiler basic operations
- [ ] Test basic functionality

### Week 2:
- [ ] Migrate VM basic operations
- [ ] Update related tests

### Week 3:
- [ ] Migrate complex operations (generics, traits)
- [ ] Full integration testing

### Week 4:
- [ ] Remove old system
- [ ] Final cleanup and documentation

## Success Criteria

- [ ] All tests pass
- [ ] No performance regression
- [ ] Code is more readable
- [ ] Type safety improved
- [ ] No runtime errors from casting

## Next Steps

1. **Start with Compiler migration** - Update `_emit` calls to use new OpCodes
2. **Test incrementally** - Ensure each change works before proceeding
3. **Update VM gradually** - Migrate switch cases one by one
4. **Comprehensive testing** - Run full test suite after each phase

This migration will significantly improve the codebase quality and developer experience while maintaining full backward compatibility during the transition.
