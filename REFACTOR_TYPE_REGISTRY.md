# Refactor: Tậ<PERSON> trung các Registry vào TypeRegistry

## Tổng quan

Đã thực hiện refactor để tập trung tất cả các registry liên quan đến type system vào trong `TypeRegistry`, thay vì phân tán trong `VirtualMachine`.

## Thay đổi chính

### 1. Mở rộng TypeRegistry

**File: `lib/core/runtime.dart`**

Đã mở rộng `TypeRegistry` để bao gồm tất cả các registry:

- **Struct definitions**: `_structRegistry` - lưu trữ định nghĩa struct
- **Interface/Trait definitions**: `_interfaceRegistry`, `_genericInterfaceRegistry` 
- **Trait implementations**: `_traitImpls`, `_staticTraitImpls`, `_genericTraitImpls`
- **Enum definitions**: `_enumRegistry`

### 2. API Methods mới trong TypeRegistry

#### Struct Registry
```dart
void registerStruct(String name, Map<String, String> fields)
Map<String, String>? getStructFields(String name)
bool hasStruct(String name)
Map<String, Map<String, String>> get structRegistry
```

#### Interface Registry
```dart
void registerInterface(String name, List<Map<String, dynamic>> methods)
List<Map<String, dynamic>>? getInterfaceMethods(String name)
bool hasInterface(String name)
void registerGenericInterface(String name, Map<String, dynamic> definition)
```

#### Trait Implementation Registry
```dart
void registerTraitImpl(String structName, String methodName, List<Instruction> instructions)
void registerTraitImpls(String structName, Map<String, List<Instruction>> methods)
List<Instruction>? getTraitImpl(String structName, String methodName)
void registerStaticTraitImpl(String structName, String methodName, List<Instruction> instructions)
void registerGenericTraitImpl(String structName, String methodName, String typeSignature, List<Instruction> instructions)
```

#### Enum Registry
```dart
void registerEnum(String name, Map<String, List<String>> variants)
Map<String, List<String>>? getEnumVariants(String name)
bool hasEnum(String name)
```

#### Context Sharing
```dart
void copyTo(TypeRegistry other)
```

### 3. Refactor VirtualMachine

**File: `lib/core/virtual_machine.dart`**

#### Loại bỏ các registry riêng biệt
Đã xóa các field sau:
- `structRegistry`
- `interfaceRegistry` 
- `traitImpls`
- `staticTraitImpls`
- `genericInterfaceRegistry`
- `genericTraitImpls`
- `enumRegistry`

#### Sử dụng TypeRegistry tập trung
Tất cả các OpCode handlers đã được cập nhật để sử dụng `typeRegistry`:

```dart
// Trước
structRegistry[name] = fields;

// Sau  
typeRegistry.registerStruct(name, fields);
```

#### Backward Compatibility
Thêm getter để đảm bảo tests hiện tại vẫn hoạt động:
```dart
Map<String, Map<String, List<Instruction>>> get staticTraitImpls => typeRegistry.staticTraitImpls;
```

#### Đơn giản hóa Context Sharing
```dart
// Trước
void copyContextTo(VirtualMachine vm) {
  vm.traitImpls = traitImpls;
  vm.staticTraitImpls = staticTraitImpls;
  vm.structRegistry = structRegistry;
  vm.interfaceRegistry = interfaceRegistry;
  vm.typeRegistry = typeRegistry;
  vm.genericInterfaceRegistry = genericInterfaceRegistry;
  vm.genericTraitImpls = genericTraitImpls;
}

// Sau
void copyContextTo(VirtualMachine vm) {
  typeRegistry.copyTo(vm.typeRegistry);
}
```

## Lợi ích

### 1. Tập trung quản lý
- Tất cả type-related data được quản lý ở một nơi
- Dễ dàng mở rộng và bảo trì
- Giảm coupling giữa VirtualMachine và type system

### 2. API nhất quán
- Tất cả registry operations đều có API tương tự
- Dễ sử dụng và hiểu
- Type-safe với proper encapsulation

### 3. Hiệu suất
- Giảm memory overhead trong VirtualMachine
- Efficient context sharing giữa VM instances
- Better cache locality cho type operations

### 4. Khả năng mở rộng
- Dễ dàng thêm new type constructs
- Centralized validation và constraint checking
- Unified type resolution logic

## Testing

Đã test với:
- `test/test_basic_features.dart` ✅
- `test/test_static_method_debug.dart` ✅

Tất cả tests hiện tại vẫn hoạt động bình thường, đảm bảo backward compatibility.

## Kết luận

Refactor này tạo ra một architecture sạch hơn và dễ bảo trì hơn cho type system của Fluent Lang, đồng thời duy trì full backward compatibility với code hiện tại.
