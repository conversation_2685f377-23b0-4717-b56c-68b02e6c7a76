// Test sau cleanup OpCode system
import 'package:fluent_lang/core/parser.dart';
import 'package:fluent_lang/core/compiler.dart';
// import 'package:fluent_lang/core/virtual_machine.dart'; // Temporarily disabled

void main() {
  print('=== Test Cleanup OpCode System ===\n');

  testBasicOperations();
}

void testBasicOperations() {
  print('--- Test: Basic Operations ---');

  final code = '''
  let x = 10;
  let y = 20;
  print(x + y);
  ''';

  try {
    final parser = buildParser(verbose: false);
    final parseResult = parser.parse(code);

    if (!parseResult.isSuccess) {
      print('❌ Parse failed: ${parseResult.message}');
      return;
    }

    print('✅ Parse successful');

    final compiler = Compiler();
    compiler.verbose = false;
    compiler.compile(parseResult.value);
    final bytecode = compiler.getBytecode();

    print('✅ Compile successful');
    print('Instructions generated: ${bytecode.instructions.length}');

    // Print instruction details
    for (int i = 0; i < bytecode.instructions.length; i++) {
      final instr = bytecode.instructions[i];
      print('  [$i] ${instr.toString()}');
    }

    // VM temporarily disabled during cleanup
    print('✅ Compiler migration successful - VM update pending\n');

  } catch (e) {
    print('❌ Error: $e\n');
  }
}
